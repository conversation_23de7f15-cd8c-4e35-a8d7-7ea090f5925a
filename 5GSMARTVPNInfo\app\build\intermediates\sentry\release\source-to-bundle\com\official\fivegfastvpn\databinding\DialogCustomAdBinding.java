// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCustomAdBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button dialogCloseButton;

  @NonNull
  public final TextView dialogDescription;

  @NonNull
  public final Button dialogGotoButton;

  @NonNull
  public final ImageView dialogImage;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final ScrollView scrollContainer;

  private DialogCustomAdBinding(@NonNull LinearLayout rootView, @NonNull Button dialogCloseButton,
      @NonNull TextView dialogDescription, @NonNull Button dialogGotoButton,
      @NonNull ImageView dialogImage, @NonNull TextView dialogTitle,
      @NonNull ScrollView scrollContainer) {
    this.rootView = rootView;
    this.dialogCloseButton = dialogCloseButton;
    this.dialogDescription = dialogDescription;
    this.dialogGotoButton = dialogGotoButton;
    this.dialogImage = dialogImage;
    this.dialogTitle = dialogTitle;
    this.scrollContainer = scrollContainer;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCustomAdBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCustomAdBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_custom_ad, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCustomAdBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dialog_close_button;
      Button dialogCloseButton = ViewBindings.findChildViewById(rootView, id);
      if (dialogCloseButton == null) {
        break missingId;
      }

      id = R.id.dialog_description;
      TextView dialogDescription = ViewBindings.findChildViewById(rootView, id);
      if (dialogDescription == null) {
        break missingId;
      }

      id = R.id.dialog_goto_button;
      Button dialogGotoButton = ViewBindings.findChildViewById(rootView, id);
      if (dialogGotoButton == null) {
        break missingId;
      }

      id = R.id.dialog_image;
      ImageView dialogImage = ViewBindings.findChildViewById(rootView, id);
      if (dialogImage == null) {
        break missingId;
      }

      id = R.id.dialog_title;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.scroll_container;
      ScrollView scrollContainer = ViewBindings.findChildViewById(rootView, id);
      if (scrollContainer == null) {
        break missingId;
      }

      return new DialogCustomAdBinding((LinearLayout) rootView, dialogCloseButton,
          dialogDescription, dialogGotoButton, dialogImage, dialogTitle, scrollContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
