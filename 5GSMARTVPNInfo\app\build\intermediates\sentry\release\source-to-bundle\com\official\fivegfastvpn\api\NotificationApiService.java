package com.official.fivegfastvpn.api;

import android.util.Log;

import com.official.fivegfastvpn.model.NotificationModel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Notification API Service for 5G Smart VPN
 * Handles communication with the admin panel notification API
 */
public class NotificationApiService {
    private static final String TAG = "NotificationApiService";
    private static NotificationApiService instance;
    private ExecutorService executor;

    private NotificationApiService() {
        executor = Executors.newFixedThreadPool(3);
    }

    public static synchronized NotificationApiService getInstance() {
        if (instance == null) {
            instance = new NotificationApiService();
        }
        return instance;
    }

    /**
     * Interface for API callbacks
     */
    public interface ApiCallback<T> {
        void onSuccess(T result);
        void onError(String error);
    }

    /**
     * Get notifications from the admin panel
     */
    public void getNotifications(int page, int limit, ApiCallback<NotificationResponse> callback) {
        executor.execute(() -> {
            try {
                String urlString = Const.NOTIFICATIONS_LIST +
                    "?page=" + page +
                    "&limit=" + limit +
                    "&sort_by=created_at&sort_order=DESC";

                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("User-Agent", "5GSMARTVPNApp/1.0");
                connection.setConnectTimeout(15000); // 15 seconds
                connection.setReadTimeout(20000);    // 20 seconds

                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);

                BufferedReader reader;
                if (responseCode >= 200 && responseCode < 300) {
                    reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                } else {
                    reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                }

                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                if (responseCode >= 200 && responseCode < 300) {
                    NotificationResponse notificationResponse = parseNotificationResponse(response.toString());
                    callback.onSuccess(notificationResponse);
                } else {
                    callback.onError("HTTP Error: " + responseCode + " - " + response.toString());
                }

            } catch (java.net.SocketTimeoutException e) {
                Log.w(TAG, "Timeout getting notifications: " + e.getMessage());
                callback.onError("Connection timeout. Please check your internet connection.");
            } catch (java.net.UnknownHostException e) {
                Log.w(TAG, "Host not found: " + e.getMessage());
                callback.onError("Cannot reach server. Please check your internet connection.");
            } catch (java.net.ConnectException e) {
                Log.w(TAG, "Connection failed: " + e.getMessage());
                callback.onError("Cannot connect to server. Please try again later.");
            } catch (Exception e) {
                Log.e(TAG, "Error getting notifications", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    /**
     * Send a notification through the admin panel API
     */
    public void sendNotification(String title, String message, String priority, String category,
                               String topic, ApiCallback<String> callback) {
        executor.execute(() -> {
            try {
                URL url = new URL(Const.NOTIFICATIONS_SEND);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(15000);

                // Create JSON payload
                JSONObject payload = new JSONObject();
                payload.put("title", title);
                payload.put("message", message);
                payload.put("priority", priority != null ? priority : "normal");
                payload.put("category", category != null ? category : "general");
                payload.put("topic", topic != null ? topic : "all");
                payload.put("schedule_type", "immediate");

                // Send the request
                OutputStream os = connection.getOutputStream();
                os.write(payload.toString().getBytes("UTF-8"));
                os.close();

                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Send Notification Response Code: " + responseCode);

                BufferedReader reader;
                if (responseCode >= 200 && responseCode < 300) {
                    reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                } else {
                    reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                }

                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                if (responseCode >= 200 && responseCode < 300) {
                    callback.onSuccess("Notification sent successfully");
                } else {
                    callback.onError("Failed to send notification: " + response.toString());
                }

            } catch (Exception e) {
                Log.e(TAG, "Error sending notification", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    /**
     * Parse the JSON response from the notifications API
     */
    private NotificationResponse parseNotificationResponse(String jsonResponse) throws JSONException {
        JSONObject jsonObject = new JSONObject(jsonResponse);

        if (!jsonObject.getBoolean("success")) {
            throw new JSONException("API returned error: " + jsonObject.optString("error", "Unknown error"));
        }

        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray notificationsArray = data.getJSONArray("notifications");
        JSONObject pagination = data.getJSONObject("pagination");

        List<NotificationModel> notifications = new ArrayList<>();

        for (int i = 0; i < notificationsArray.length(); i++) {
            JSONObject notificationJson = notificationsArray.getJSONObject(i);
            NotificationModel notification = parseNotificationModel(notificationJson);
            notifications.add(notification);
        }

        NotificationResponse response = new NotificationResponse();
        response.setNotifications(notifications);
        response.setCurrentPage(pagination.getInt("current_page"));
        response.setTotalPages(pagination.getInt("total_pages"));
        response.setTotalCount(pagination.getInt("total_count"));
        response.setHasNext(pagination.getBoolean("has_next"));
        response.setHasPrev(pagination.getBoolean("has_prev"));

        return response;
    }

    /**
     * Parse a single notification from JSON
     */
    private NotificationModel parseNotificationModel(JSONObject json) throws JSONException {
        NotificationModel notification = new NotificationModel();

        notification.setId(json.getInt("id"));
        notification.setTitle(json.getString("title"));
        notification.setMessage(json.getString("message"));
        notification.setMessagePreview(json.optString("message_preview", ""));
        notification.setStatus(json.getString("status"));
        notification.setSentTo(json.optString("sent_to", ""));
        notification.setNotificationType(json.optString("notification_type", ""));
        notification.setScheduleType(json.optString("schedule_type", ""));
        notification.setScheduledTime(json.optString("scheduled_time", ""));
        notification.setRecurringInterval(json.optString("recurring_interval", ""));
        notification.setPriority(json.optString("priority", "normal"));
        notification.setCategory(json.optString("category", "general"));
        notification.setTargetAudience(json.optString("target_audience", ""));
        notification.setDeliveryCount(json.optInt("delivery_count", 0));
        notification.setSuccessCount(json.optInt("success_count", 0));
        notification.setFailureCount(json.optInt("failure_count", 0));
        notification.setCreatedAt(json.getString("created_at"));
        notification.setUpdatedAt(json.optString("updated_at", ""));

        return notification;
    }

    /**
     * Response wrapper for notification list API
     */
    public static class NotificationResponse {
        private List<NotificationModel> notifications;
        private int currentPage;
        private int totalPages;
        private int totalCount;
        private boolean hasNext;
        private boolean hasPrev;

        // Getters and setters
        public List<NotificationModel> getNotifications() {
            return notifications;
        }

        public void setNotifications(List<NotificationModel> notifications) {
            this.notifications = notifications;
        }

        public int getCurrentPage() {
            return currentPage;
        }

        public void setCurrentPage(int currentPage) {
            this.currentPage = currentPage;
        }

        public int getTotalPages() {
            return totalPages;
        }

        public void setTotalPages(int totalPages) {
            this.totalPages = totalPages;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public boolean isHasNext() {
            return hasNext;
        }

        public void setHasNext(boolean hasNext) {
            this.hasNext = hasNext;
        }

        public boolean isHasPrev() {
            return hasPrev;
        }

        public void setHasPrev(boolean hasPrev) {
            this.hasPrev = hasPrev;
        }
    }
}
