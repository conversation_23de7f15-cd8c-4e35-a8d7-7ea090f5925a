[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\fragment_free_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\fragment_free_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\native_item_ads_container.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\native_item_ads_container.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\content_servers.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\content_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\item_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\item_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\dialog_vpn_warning.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\dialog_vpn_warning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\fragment_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\fragment_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\fragment_vip_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\fragment_vip_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\native_ad_item_facebook.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\native_ad_item_facebook.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\item_servers_premium.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\item_servers_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\admob_nativead_templete.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\admob_nativead_templete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\item_premium.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\item_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\activity_notifications.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\activity_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\dialog_contact_us.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\dialog_contact_us.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\activity_main_drawer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\activity_main_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\activity_servers.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\activity_servers.xml"}, {"merged": "com.official.fivegfastvpn.app-mergeDebugResources-77:/layout/activity_ad_debug.xml", "source": "com.official.fivegfastvpn.app-main-80:/layout/activity_ad_debug.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\toolbar.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\toolbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\item_notification.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\nav_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\native_ads_item_admob.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\native_ads_item_admob.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\pro_activity_premium.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\pro_activity_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\dialog_custom_ad.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\dialog_custom_ad.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\dialog_about_us.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\dialog_about_us.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\activity_splash_screen.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\activity_splash_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-mergeDebugResources-77:\\layout\\fragment_all_servers.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-80:\\layout\\fragment_all_servers.xml"}]