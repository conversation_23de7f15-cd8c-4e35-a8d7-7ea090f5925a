package com.official.fivegfastvpn.ads;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.facebook.ads.Ad;
import com.facebook.ads.AdError;
import com.facebook.ads.AdOptionsView;
import com.facebook.ads.AdView;
import com.facebook.ads.AudienceNetworkAds;
import com.facebook.ads.InterstitialAdListener;
import com.facebook.ads.MediaView;
import com.facebook.ads.NativeAdLayout;
import com.facebook.ads.RewardedVideoAd;
import com.facebook.ads.RewardedVideoAdListener;
import com.official.fivegfastvpn.R;


import java.util.ArrayList;
import java.util.List;
//Developer :--Md Sadrul Hasan Dider
public class Facebook {

    public static AdCall adCalls;
    public static AdCall adRewCalls;


    public static void setBanner(Activity context, LinearLayout bannerContainer) {
        AdView adView = new AdView(context, AdsHelper.facebook_banner, com.facebook.ads.AdSize.BANNER_HEIGHT_50);
        bannerContainer.addView(adView);
        adView.loadAd();
    }

    public static void loadInt(Activity activity) {
        AudienceNetworkAds.initialize(activity);

        AdCode.fInterstitial = new com.facebook.ads.InterstitialAd(activity, AdsHelper.facebook_interstitial);
        InterstitialAdListener interstitialAdListener = new InterstitialAdListener() {
            @Override
            public void onError(Ad ad, AdError adError) {
            }

            @Override
            public void onAdLoaded(Ad ad) {
            }

            @Override
            public void onAdClicked(Ad ad) {
            }

            @Override
            public void onLoggingImpression(Ad ad) {
            }

            @Override
            public void onInterstitialDisplayed(Ad ad) {
            }

            @Override
            public void onInterstitialDismissed(Ad ad) {
                AdCode.fInterstitial = null;
                AdCode.loadInt(activity);
                adCalls.next();
            }
        };
        // load the ad
        AdCode.fInterstitial.loadAd(AdCode.fInterstitial.buildLoadAdConfig().withAdListener(interstitialAdListener).build());
    }


    public static void showInt(Activity activity, AdCall adCall) {

        adCalls = adCall;

        if (AdCode.fInterstitial == null || !AdCode.fInterstitial.isAdLoaded()) {
            adCall.next();
            return;
        }
        if (AdCode.fInterstitial.isAdInvalidated()) {
            adCall.next();
            return;
        }
        AdCode.fInterstitial.show();
    }


    public static void loadRew(Activity activity) {
        AudienceNetworkAds.initialize(activity);
        AdCode.fRewarded = new RewardedVideoAd(activity, AdsHelper.facebook_rewarded);
        RewardedVideoAdListener rewardedVideoAdListener = new RewardedVideoAdListener() {
            @Override
            public void onError(Ad ad, AdError error) {

            }

            @Override
            public void onAdLoaded(Ad ad) {

            }

            @Override
            public void onAdClicked(Ad ad) {

            }

            @Override
            public void onLoggingImpression(Ad ad) {

            }

            @Override
            public void onRewardedVideoCompleted() {

            }

            @Override
            public void onRewardedVideoClosed() {
                AdCode.fRewarded = null;
                Facebook.loadRew(activity);
                Facebook.adRewCalls.next();
            }
        };
        AdCode.fRewarded.loadAd(AdCode.fRewarded.buildLoadAdConfig().withAdListener(rewardedVideoAdListener).build());
    }

    public static void showRew(Activity context, AdCall adCallBacks) {
        Facebook.adRewCalls = adCallBacks;

        if (AdCode.fRewarded == null || !AdCode.fRewarded.isAdLoaded()) {
            adCallBacks.failed();
            return;
        }
        if (AdCode.fRewarded.isAdInvalidated()) {
            adCallBacks.failed();
            return;
        }
        AdCode.fRewarded.show();
    }


//    ========================== native =================

    public static void inflateAd(com.facebook.ads.NativeAd nativeAd, NativeAdLayout nativeAdLayout, Activity context) {

        nativeAd.unregisterView();
        LayoutInflater inflater = LayoutInflater.from(context);
        View adView = inflater.inflate(R.layout.native_ad_item_facebook, nativeAdLayout, false);
        nativeAdLayout.addView(adView);
        LinearLayout adChoicesContainer = context.findViewById(R.id.ad_choices_container);
        AdOptionsView adOptionsView = new AdOptionsView(context, nativeAd, nativeAdLayout);
        adChoicesContainer.removeAllViews();
        adChoicesContainer.addView(adOptionsView, 0);

        MediaView nativeAdIcon = adView.findViewById(R.id.native_ad_icon);
        TextView nativeAdTitle = adView.findViewById(R.id.native_ad_title);
        MediaView nativeAdMedia = adView.findViewById(R.id.native_ad_media);
        TextView nativeAdSocialContext = adView.findViewById(R.id.native_ad_social_context);
        TextView nativeAdBody = adView.findViewById(R.id.native_ad_body);
        TextView sponsoredLabel = adView.findViewById(R.id.native_ad_sponsored_label);
        Button nativeAdCallToAction = adView.findViewById(R.id.native_ad_call_to_action);

        nativeAdTitle.setText(nativeAd.getAdvertiserName());
        nativeAdBody.setText(nativeAd.getAdBodyText());
        nativeAdSocialContext.setText(nativeAd.getAdSocialContext());
        nativeAdCallToAction.setVisibility(nativeAd.hasCallToAction() ? View.VISIBLE : View.INVISIBLE);
        nativeAdCallToAction.setText(nativeAd.getAdCallToAction());
        sponsoredLabel.setText(nativeAd.getSponsoredTranslation());

        List<View> clickableViews = new ArrayList<>();
        clickableViews.add(nativeAdTitle);
        clickableViews.add(nativeAdCallToAction);
        clickableViews.add(nativeAdIcon);

        nativeAd.registerViewForInteraction(adView, nativeAdMedia, nativeAdIcon, clickableViews);

    }

}
