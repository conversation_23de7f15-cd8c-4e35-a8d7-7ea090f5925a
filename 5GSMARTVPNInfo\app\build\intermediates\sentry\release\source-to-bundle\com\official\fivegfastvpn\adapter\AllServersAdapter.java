package com.official.fivegfastvpn.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.model.Server;

import java.util.ArrayList;

public class AllServersAdapter extends RecyclerView.Adapter<AllServersAdapter.MyViewHolder> {

    private ArrayList<Server> servers = new ArrayList<>();
    private final Context context;
    private OnSelectListener listener;

    public AllServersAdapter(Context context) {
        this.context = context;
    }

    public void setServers(ArrayList<Server> servers) {
        Log.d("ServerDebug", "Setting servers in adapter, count: " + (servers != null ? servers.size() : 0));
        this.servers = servers;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Log.d("ServerDebug", "Creating new ViewHolder");
        View view = LayoutInflater.from(context).inflate(R.layout.item_server, parent, false);
        return new MyViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolder holder, int position) {
        Log.d("ServerDebug", "Binding ViewHolder at position: " + position);
        Server server = servers.get(position);
        Glide.with(context)
                .load(server.getFlagUrl())
                .into(holder.flag);

        holder.country.setText(server.getCountry());
        
        // Show VIP badge for premium servers
        if (server.getType() != null && server.getType().equals("0")) {
            holder.vipBadge.setVisibility(View.VISIBLE);
        } else {
            holder.vipBadge.setVisibility(View.GONE);
        }

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onSelected(server);
            }
        });
    }

    @Override
    public int getItemCount() {
        return servers.size();
    }

    public void setOnSelectListener(OnSelectListener listener) {
        this.listener = listener;
    }

    public interface OnSelectListener {
        void onSelected(Server server);
    }

    static class MyViewHolder extends RecyclerView.ViewHolder {
        ImageView flag;
        TextView country;
        ImageView vipBadge;

        MyViewHolder(@NonNull View itemView) {
            super(itemView);
            flag = itemView.findViewById(R.id.flag);
            country = itemView.findViewById(R.id.countryName);
            vipBadge = itemView.findViewById(R.id.vip_badge);
        }
    }
}
