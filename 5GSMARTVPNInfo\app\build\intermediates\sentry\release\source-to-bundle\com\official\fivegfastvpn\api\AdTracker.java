package com.official.fivegfastvpn.api;

import android.content.Context;
import android.provider.Settings;
import android.util.Log;

/**
 * Ad Tracking Utility for 5G Smart VPN
 * Handles tracking of ad views and clicks
 */
public class AdTracker {
    private static final String TAG = "AdTracker";
    
    /**
     * Track custom ad view
     */
    public static void trackCustomAdView(Context context, int adId) {
        trackAd(context, adId, "view", "custom");
    }
    
    /**
     * Track custom ad click
     */
    public static void trackCustomAdClick(Context context, int adId) {
        trackAd(context, adId, "click", "custom");
    }
    
    /**
     * Track AdMob ad view
     */
    public static void trackAdMobView(Context context, String adUnitId) {
        // Use hash of ad unit ID as ad ID for tracking
        int adId = adUnitId.hashCode();
        trackAd(context, adId, "view", "admob");
    }
    
    /**
     * Track AdMob ad click
     */
    public static void trackAdMobClick(Context context, String adUnitId) {
        // Use hash of ad unit ID as ad ID for tracking
        int adId = adUnitId.hashCode();
        trackAd(context, adId, "click", "admob");
    }
    
    /**
     * Track Facebook ad view
     */
    public static void trackFacebookView(Context context, String placementId) {
        // Use hash of placement ID as ad ID for tracking
        int adId = placementId.hashCode();
        trackAd(context, adId, "view", "facebook");
    }
    
    /**
     * Track Facebook ad click
     */
    public static void trackFacebookClick(Context context, String placementId) {
        // Use hash of placement ID as ad ID for tracking
        int adId = placementId.hashCode();
        trackAd(context, adId, "click", "facebook");
    }
    
    /**
     * Generic ad tracking method
     */
    private static void trackAd(Context context, int adId, String eventType, String adType) {
        try {
            VpnApiService apiService = VpnApiService.getInstance(context);
            String deviceId = getDeviceId(context);
            String userId = getUserId(context);
            
            apiService.trackAd(adId, eventType, adType, userId, deviceId, 
                new VpnApiService.ApiCallback<String>() {
                    @Override
                    public void onSuccess(String result) {
                        Log.d(TAG, "Ad tracking successful: " + result);
                    }
                    
                    @Override
                    public void onError(String error, int errorCode) {
                        Log.w(TAG, "Ad tracking failed: " + error + " (Code: " + errorCode + ")");
                        // Don't show error to user, just log it
                    }
                });
                
        } catch (Exception e) {
            Log.e(TAG, "Error tracking ad", e);
        }
    }
    
    /**
     * Get device ID for tracking
     */
    private static String getDeviceId(Context context) {
        try {
            return Settings.Secure.getString(context.getContentResolver(), 
                Settings.Secure.ANDROID_ID);
        } catch (Exception e) {
            Log.w(TAG, "Could not get device ID", e);
            return "unknown";
        }
    }
    
    /**
     * Get user ID for tracking (can be customized based on your user system)
     */
    private static String getUserId(Context context) {
        // For now, return null. You can implement user identification here
        // if you have a user system in your app
        return null;
    }
    
    /**
     * Track app open ad specifically
     */
    public static void trackAppOpenAd(Context context, String eventType) {
        // Use a fixed ID for app open ads
        trackAd(context, 999999, eventType, "admob");
    }
    
    /**
     * Track banner ad
     */
    public static void trackBannerAd(Context context, String eventType, String adType) {
        // Use a fixed ID for banner ads
        trackAd(context, 888888, eventType, adType);
    }
    
    /**
     * Track interstitial ad
     */
    public static void trackInterstitialAd(Context context, String eventType, String adType) {
        // Use a fixed ID for interstitial ads
        trackAd(context, 777777, eventType, adType);
    }
    
    /**
     * Track rewarded ad
     */
    public static void trackRewardedAd(Context context, String eventType, String adType) {
        // Use a fixed ID for rewarded ads
        trackAd(context, 666666, eventType, adType);
    }
    
    /**
     * Track native ad
     */
    public static void trackNativeAd(Context context, String eventType, String adType) {
        // Use a fixed ID for native ads
        trackAd(context, 555555, eventType, adType);
    }
}
