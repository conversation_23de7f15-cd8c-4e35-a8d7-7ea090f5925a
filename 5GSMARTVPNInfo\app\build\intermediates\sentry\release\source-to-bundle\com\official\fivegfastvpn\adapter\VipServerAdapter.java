package com.official.fivegfastvpn.adapter;

//Developer :--<PERSON><PERSON><PERSON>er


import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.model.Server;
import com.official.fivegfastvpn.pro.PremiumActivity;
import com.official.fivegfastvpn.pro.ProConfig;


import java.util.ArrayList;
import java.util.List;

public class VipServerAdapter extends RecyclerView.Adapter<VipServerAdapter.MyViewHolder> {

    ArrayList<Server> serverLists;
    Context mContext;
    OnSelectListener selectListener;

    public VipServerAdapter(Context context) {
        this.mContext = context;
        serverLists = new ArrayList<>();
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_servers_premium, parent, false);
        return new MyViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolder holder, int position) {

        holder.serverCountry.setText(serverLists.get(position).getCountry());
        Glide.with(mContext)
                .load(serverLists.get(position).getFlagUrl())
                .into(holder.serverIcon);

        holder.itemView.setOnClickListener(v -> {

            if (ProConfig.isPremium(mContext)) {
                selectListener.onSelected(serverLists.get(position));
            } else {
                Toast.makeText(mContext, "Upgrade App to use premium servers.", Toast.LENGTH_SHORT).show();
                mContext.startActivity(new Intent(mContext, PremiumActivity.class));
            }
        });
    }

    @Override
    public int getItemCount() {
        return serverLists.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        ImageView serverIcon;
        TextView serverCountry;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            serverIcon = itemView.findViewById(R.id.flag);
            serverCountry = itemView.findViewById(R.id.countryName);
        }
    }

    public void setData(List<Server> servers) {
        serverLists.clear();
        serverLists.addAll(servers);
        notifyDataSetChanged();
    }

    public interface OnSelectListener {
        void onSelected(Server server);
    }

    public void setOnSelectListener(OnSelectListener selectListener) {
        this.selectListener = selectListener;
    }
}
