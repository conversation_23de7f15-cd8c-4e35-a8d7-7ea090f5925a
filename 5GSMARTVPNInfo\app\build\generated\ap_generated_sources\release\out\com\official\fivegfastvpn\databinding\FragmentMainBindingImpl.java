package com.official.fivegfastvpn.databinding;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.BR;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
@SuppressWarnings("unchecked")
public class FragmentMainBindingImpl extends FragmentMainBinding  {

    @Nullable
    private static final androidx.databinding.ViewDataBinding.IncludedLayouts sIncludes;
    @Nullable
    private static final android.util.SparseIntArray sViewsWithIds;
    static {
        sIncludes = null;
        sViewsWithIds = new android.util.SparseIntArray();
        sViewsWithIds.put(R.id.toolbar, 3);
        sViewsWithIds.put(R.id.category, 4);
        sViewsWithIds.put(R.id.text, 5);
        sViewsWithIds.put(R.id.notification, 6);
        sViewsWithIds.put(R.id.premium, 7);
        sViewsWithIds.put(R.id.pre, 8);
        sViewsWithIds.put(R.id.purchase_layout, 9);
        sViewsWithIds.put(R.id.currentConnectionLayout, 10);
        sViewsWithIds.put(R.id.selectedServerIcon, 11);
        sViewsWithIds.put(R.id.countryName, 12);
        sViewsWithIds.put(R.id.ipTv, 13);
        sViewsWithIds.put(R.id.chevronRight, 14);
        sViewsWithIds.put(R.id.protectionStatus, 15);
        sViewsWithIds.put(R.id.contime1, 16);
        sViewsWithIds.put(R.id.durationTv, 17);
        sViewsWithIds.put(R.id.ipAddress, 18);
        sViewsWithIds.put(R.id.statsLayout, 19);
        sViewsWithIds.put(R.id.byteInTv, 20);
        sViewsWithIds.put(R.id.byteOutTv, 21);
        sViewsWithIds.put(R.id.btnConnect, 22);
        sViewsWithIds.put(R.id.logTv, 23);
        sViewsWithIds.put(R.id.renewButtonLayout, 24);
        sViewsWithIds.put(R.id.btnRenew, 25);
        sViewsWithIds.put(R.id.extraTime, 26);
    }
    // views
    @NonNull
    private final androidx.core.widget.NestedScrollView mboundView0;
    @NonNull
    private final android.widget.LinearLayout mboundView1;
    @Nullable
    private final com.official.fivegfastvpn.databinding.NativeItemAdsContainerBinding mboundView11;
    // variables
    // values
    // listeners
    // Inverse Binding Event Handlers

    public FragmentMainBindingImpl(@Nullable androidx.databinding.DataBindingComponent bindingComponent, @NonNull View root) {
        this(bindingComponent, root, mapBindings(bindingComponent, root, 27, sIncludes, sViewsWithIds));
    }
    private FragmentMainBindingImpl(androidx.databinding.DataBindingComponent bindingComponent, View root, Object[] bindings) {
        super(bindingComponent, root, 0
            , (com.airbnb.lottie.LottieAnimationView) bindings[22]
            , (android.widget.Button) bindings[25]
            , (android.widget.TextView) bindings[20]
            , (android.widget.TextView) bindings[21]
            , (android.widget.ImageView) bindings[4]
            , (android.widget.ImageView) bindings[14]
            , (android.widget.LinearLayout) bindings[16]
            , (android.widget.TextView) bindings[12]
            , (android.widget.RelativeLayout) bindings[10]
            , (android.widget.TextView) bindings[17]
            , (android.widget.Button) bindings[26]
            , (android.widget.TextView) bindings[18]
            , (android.widget.TextView) bindings[13]
            , (android.widget.TextView) bindings[23]
            , (android.widget.ImageView) bindings[6]
            , (android.widget.LinearLayout) bindings[8]
            , (android.widget.ImageView) bindings[7]
            , (android.widget.TextView) bindings[15]
            , (android.widget.LinearLayout) bindings[9]
            , (android.widget.LinearLayout) bindings[24]
            , (android.widget.ImageView) bindings[11]
            , (android.widget.LinearLayout) bindings[19]
            , (android.widget.TextView) bindings[5]
            , (android.widget.RelativeLayout) bindings[3]
            );
        this.mboundView0 = (androidx.core.widget.NestedScrollView) bindings[0];
        this.mboundView0.setTag(null);
        this.mboundView1 = (android.widget.LinearLayout) bindings[1];
        this.mboundView1.setTag(null);
        this.mboundView11 = (bindings[2] != null) ? com.official.fivegfastvpn.databinding.NativeItemAdsContainerBinding.bind((android.view.View) bindings[2]) : null;
        setRootTag(root);
        // listeners
        invalidateAll();
    }

    @Override
    public void invalidateAll() {
        synchronized(this) {
                mDirtyFlags = 0x1L;
        }
        requestRebind();
    }

    @Override
    public boolean hasPendingBindings() {
        synchronized(this) {
            if (mDirtyFlags != 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean setVariable(int variableId, @Nullable Object variable)  {
        boolean variableSet = true;
            return variableSet;
    }

    @Override
    protected boolean onFieldChange(int localFieldId, Object object, int fieldId) {
        switch (localFieldId) {
        }
        return false;
    }

    @Override
    protected void executeBindings() {
        long dirtyFlags = 0;
        synchronized(this) {
            dirtyFlags = mDirtyFlags;
            mDirtyFlags = 0;
        }
        // batch finished
    }
    // Listener Stub Implementations
    // callback impls
    // dirty flag
    private  long mDirtyFlags = 0xffffffffffffffffL;
    /* flag mapping
        flag 0 (0x1L): null
    flag mapping end*/
    //end
}