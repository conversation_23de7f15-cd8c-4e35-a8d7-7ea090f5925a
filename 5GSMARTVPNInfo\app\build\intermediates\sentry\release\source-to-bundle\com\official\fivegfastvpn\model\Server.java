package com.official.fivegfastvpn.model;


import android.os.Parcel;
import android.os.Parcelable;

public class Server implements Parcelable {

    private String country;
    private String flagUrl;
    private String ovpn;
    private String ovpnUserName;
    private String ovpnUserPassword;
    private String type;

    // Additional fields for API compatibility
    private int id;
    private int pos;
    private int status;

    public Server() {
    }

    public Server(String country, String flagUrl, String ovpn) {
        this.country = country;
        this.flagUrl = flagUrl;
        this.ovpn = ovpn;
    }

    public Server(String country, String flagUrl, String ovpn, String ovpnUserName, String ovpnUserPassword) {
        this.country = country;
        this.flagUrl = flagUrl;
        this.ovpn = ovpn;
        this.ovpnUserName = ovpnUserName;
        this.ovpnUserPassword = ovpnUserPassword;
    }

    public Server(String country, String flagUrl, String ovpn, String ovpnUserName, String ovpnUserPassword, String type) {
        this.country = country;
        this.flagUrl = flagUrl;
        this.ovpn = ovpn;
        this.ovpnUserName = ovpnUserName;
        this.ovpnUserPassword = ovpnUserPassword;
        this.type = type;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getFlagUrl() {
        return flagUrl;
    }

    public void setFlagUrl(String flagUrl) {
        this.flagUrl = flagUrl;
    }

    public String getOvpn() {
        return ovpn;
    }

    public void setOvpn(String ovpn) {
        this.ovpn = ovpn;
    }

    public String getOvpnUserName() {
        return ovpnUserName;
    }

    public void setOvpnUserName(String ovpnUserName) {
        this.ovpnUserName = ovpnUserName;
    }

    public String getOvpnUserPassword() {
        return ovpnUserPassword;
    }

    public void setOvpnUserPassword(String ovpnUserPassword) {
        this.ovpnUserPassword = ovpnUserPassword;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    // Additional getters and setters for API compatibility
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPos() {
        return pos;
    }

    public void setPos(int pos) {
        this.pos = pos;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    // Alias methods for API compatibility
    public String getName() {
        return country;
    }

    public void setName(String name) {
        this.country = name;
    }

    public String getUsername() {
        return ovpnUserName;
    }

    public void setUsername(String username) {
        this.ovpnUserName = username;
    }

    public String getPassword() {
        return ovpnUserPassword;
    }

    public void setPassword(String password) {
        this.ovpnUserPassword = password;
    }

    public String getConfigFile() {
        return ovpn;
    }

    public void setConfigFile(String configFile) {
        this.ovpn = configFile;
    }

    public String getFlagURL() {
        return flagUrl;
    }

    public void setFlagURL(String flagURL) {
        this.flagUrl = flagURL;
    }

    public static final Creator<Server> CREATOR
            = new Creator<Server>() {
        public Server createFromParcel(Parcel in) {
            return new Server(in);
        }

        public Server[] newArray(int size) {
            return new Server[size];
        }
    };

    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(country);
        dest.writeString(flagUrl);
        dest.writeString(ovpn);
        dest.writeString(ovpnUserName);
        dest.writeString(ovpnUserPassword);
        dest.writeString(type);
        dest.writeInt(id);
        dest.writeInt(pos);
        dest.writeInt(status);
    }

    private Server(Parcel in ) {
        country = in.readString();
        flagUrl = in.readString();
        ovpn = in.readString();
        ovpnUserName = in.readString();
        ovpnUserPassword = in.readString();
        type = in.readString();
        id = in.readInt();
        pos = in.readInt();
        status = in.readInt();
    }
}
