// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentFreeServerBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerFree;

  @NonNull
  public final ImageButton refreshButton;

  @NonNull
  public final SearchView searchView1;

  @NonNull
  public final LinearLayout searchandrefresh;

  private FragmentFreeServerBinding(@NonNull RelativeLayout rootView,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerFree,
      @NonNull ImageButton refreshButton, @NonNull SearchView searchView1,
      @NonNull LinearLayout searchandrefresh) {
    this.rootView = rootView;
    this.progressBar = progressBar;
    this.recyclerFree = recyclerFree;
    this.refreshButton = refreshButton;
    this.searchView1 = searchView1;
    this.searchandrefresh = searchandrefresh;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentFreeServerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentFreeServerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_free_server, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentFreeServerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_free;
      RecyclerView recyclerFree = ViewBindings.findChildViewById(rootView, id);
      if (recyclerFree == null) {
        break missingId;
      }

      id = R.id.refreshButton;
      ImageButton refreshButton = ViewBindings.findChildViewById(rootView, id);
      if (refreshButton == null) {
        break missingId;
      }

      id = R.id.searchView1;
      SearchView searchView1 = ViewBindings.findChildViewById(rootView, id);
      if (searchView1 == null) {
        break missingId;
      }

      id = R.id.searchandrefresh;
      LinearLayout searchandrefresh = ViewBindings.findChildViewById(rootView, id);
      if (searchandrefresh == null) {
        break missingId;
      }

      return new FragmentFreeServerBinding((RelativeLayout) rootView, progressBar, recyclerFree,
          refreshButton, searchView1, searchandrefresh);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
