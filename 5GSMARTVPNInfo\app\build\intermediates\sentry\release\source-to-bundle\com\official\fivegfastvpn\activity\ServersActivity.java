package com.official.fivegfastvpn.activity;

//Developer :--<PERSON><PERSON><PERSON>er

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentTransaction;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.fragments.AllServersFragment;
import com.official.fivegfastvpn.utils.Utils;
import com.official.fivegfastvpn.ads.AdCode;

public class ServersActivity extends AppCompatActivity {

    public static boolean resume;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_servers);

        // Add the AllServersFragment to the container
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.fragment_container, new AllServersFragment());
        transaction.commit();

        Utils.setTools("Servers", ServersActivity.this);
        AdCode.setBanner(findViewById(R.id.ad_container_servers), ServersActivity.this);
    }
}
