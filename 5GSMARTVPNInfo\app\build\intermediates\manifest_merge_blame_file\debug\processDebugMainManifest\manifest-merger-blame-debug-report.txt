1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.fivegfastvpn"
4    android:versionCode="13"
5    android:versionName="13" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:5-77
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:5-87
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:22-84
15    <uses-permission android:name="com.android.vending.BILLING" />
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:5-67
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:22-64
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:5-77
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:22-74
17
18    <!-- VPN Service Permissions -->
19    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:5-13:47
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:22-72
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:5-78
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:22-75
21    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:22-78
22
23    <queries>
23-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
24        <intent>
24-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
25            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
26        </intent>
27        <intent>
27-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
28            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
29        </intent>
30
31        <package android:name="com.facebook.katana" /> <!-- For browser content -->
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent> <!-- End of browser content -->
39        <!-- For CustomTabsService -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
42        </intent> <!-- End of CustomTabsService -->
43        <!-- For MRAID capabilities -->
44        <intent>
44-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
45            <action android:name="android.intent.action.INSERT" />
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
46
47            <data android:mimeType="vnd.android.cursor.dir/event" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
48        </intent>
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
50            <action android:name="android.intent.action.VIEW" />
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
51
52            <data android:scheme="sms" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
55            <action android:name="android.intent.action.DIAL" />
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
56
57            <data android:path="tel:" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
62    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
67
68    <permission
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:16:5-98:15
75        android:name="com.official.fivegfastvpn.VPNApplication"
75-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:17:9-39
76        android:allowBackup="true"
76-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:18:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:dataExtractionRules="@xml/data_extraction_rules"
78-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:19:9-65
79        android:debuggable="true"
80        android:extractNativeLibs="true"
81        android:fullBackupContent="@xml/backup_rules"
81-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:20:9-54
82        android:icon="@mipmap/ic_launcher"
82-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:22:9-43
83        android:label="@string/app_name"
83-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:23:9-41
84        android:networkSecurityConfig="@xml/network_security_config"
84-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:21:9-69
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:24:9-54
86        android:supportsRtl="true"
86-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:25:9-35
87        android:testOnly="true"
88        android:theme="@style/Base.Theme._5GSMARTVPNInfo"
88-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:27:9-58
89        android:usesCleartextTraffic="true" >
89-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:26:9-44
90        <activity android:name="com.official.fivegfastvpn.activity.NotificationsActivity" />
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:9-67
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:19-65
91        <activity android:name="com.official.fivegfastvpn.pro.PremiumActivity" />
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:9-57
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:19-54
92        <activity android:name="com.official.fivegfastvpn.MainActivity" />
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:9-50
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:19-47
93        <activity android:name="com.official.fivegfastvpn.activity.ServersActivity" />
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:9-62
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:19-59
94        <activity
94-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:33:9-38:46
95            android:name="com.official.fivegfastvpn.activity.VpnWarningActivity"
95-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:34:13-56
96            android:excludeFromRecents="true"
96-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:36:13-46
97            android:launchMode="singleTop"
97-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:38:13-43
98            android:taskAffinity=""
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:13-36
99            android:theme="@style/CustomAlertDialog" />
99-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:35:13-53
100        <activity
100-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:9-47:20
101            android:name="com.official.fivegfastvpn.SplashActivity"
101-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:40:13-43
102            android:exported="true" >
102-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:41:13-36
103            <intent-filter>
103-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:42:13-46:29
104                <action android:name="android.intent.action.MAIN" />
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:43:17-69
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:43:25-66
105
106                <category android:name="android.intent.category.LAUNCHER" />
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:45:17-77
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:45:27-74
107            </intent-filter>
108        </activity>
109        <activity
109-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:48:9-53:52
110            android:name="de.blinkt.openvpn.DisconnectVPNActivity"
110-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:49:13-67
111            android:excludeFromRecents="true"
111-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:50:13-46
112            android:noHistory="true"
112-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:51:13-37
113            android:taskAffinity=".DisconnectVPN"
113-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:52:13-50
114            android:theme="@style/blinkt.dialog" />
114-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:53:13-49
115
116        <service
116-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:9-63:19
117            android:name="de.blinkt.openvpn.core.OpenVPNService"
117-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:56:13-65
118            android:exported="true"
118-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:57:13-36
119            android:foregroundServiceType="dataSync|dataSync"
119-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:58:13-53
120            android:permission="android.permission.BIND_VPN_SERVICE" >
120-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:59:13-69
121            <intent-filter>
121-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:60:13-62:29
122                <action android:name="android.net.VpnService" />
122-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:17-65
122-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:25-62
123            </intent-filter>
124        </service>
125
126        <!-- VPN Timer Background Service -->
127        <service
127-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:66:9-69:56
128            android:name="com.official.fivegfastvpn.service.VpnTimerService"
128-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:67:13-52
129            android:exported="false"
129-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:68:13-37
130            android:foregroundServiceType="dataSync" />
130-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:69:13-53
131        <service
131-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:70:9-76:19
132            android:name="com.official.fivegfastvpn.MyFirebaseMessagingService"
132-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:71:13-55
133            android:exported="false" >
133-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:13-37
134            <intent-filter>
134-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:73:13-75:29
135                <action android:name="com.google.firebase.MESSAGING_EVENT" />
135-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:74:17-78
135-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:74:25-75
136            </intent-filter>
137        </service>
138
139        <meta-data
139-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:9-80:70
140            android:name="com.google.android.gms.ads.APPLICATION_ID"
140-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:79:13-69
141            android:value="ca-app-pub-5193340328939721~2015388624" />
141-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:13-67
142        <meta-data
142-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:9-84:32
143            android:name="com.facebook.sdk.ApplicationId"
143-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:83:13-58
144            android:value="" />
144-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:84:13-29
145
146        <!-- Required: set your sentry.io project identifier (DSN) -->
147        <meta-data
147-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:87:5-159
148            android:name="io.sentry.dsn"
148-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:87:16-44
149            android:value="https://<EMAIL>/4508793236488192" />
149-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:87:45-156
150
151        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
152        <meta-data
152-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:90:5-95
153            android:name="io.sentry.traces.user-interaction.enable"
153-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:90:16-71
154            android:value="true" />
154-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:90:72-92
155        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
156        <meta-data
156-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:5-82
157            android:name="io.sentry.attach-screenshot"
157-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:16-58
158            android:value="true" />
158-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:59-79
159        <!-- enable view hierarchy for crashes -->
160        <meta-data
160-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:94:5-86
161            android:name="io.sentry.attach-view-hierarchy"
161-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:94:16-62
162            android:value="true" />
162-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:94:63-83
163
164        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
165        <meta-data
165-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:97:5-82
166            android:name="io.sentry.traces.sample-rate"
166-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:97:16-59
167            android:value="1.0" />
167-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:97:60-79
168        <!-- VpnService-based OpenVPN implementation (V2) -->
169        <service
169-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-27:19
170            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
170-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-67
171            android:exported="true"
171-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
172            android:foregroundServiceType="dataSync"
172-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-53
173            android:permission="android.permission.BIND_VPN_SERVICE" >
173-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-69
174            <intent-filter>
174-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:60:13-62:29
175                <action android:name="android.net.VpnService" />
175-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:17-65
175-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:25-62
176            </intent-filter>
177        </service>
178
179        <meta-data
179-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
180            android:name="com.google.android.play.billingclient.version"
180-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
181            android:value="7.1.1" />
181-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
182
183        <activity
183-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
184            android:name="com.android.billingclient.api.ProxyBillingActivity"
184-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
185            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
185-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
186            android:exported="false"
186-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
187            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
187-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
188        <activity
188-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
189            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
189-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
190            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
190-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
191            android:exported="false"
191-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
192            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
192-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
193
194        <receiver
194-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
195            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
195-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
196            android:exported="true"
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
197            android:permission="com.google.android.c2dm.permission.SEND" >
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
198            <intent-filter>
198-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
199                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
199-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
199-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
200            </intent-filter>
201
202            <meta-data
202-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
203                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
203-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
204                android:value="true" />
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
205        </receiver>
206        <!--
207             FirebaseMessagingService performs security checks at runtime,
208             but set to not exported to explicitly avoid allowing another app to call it.
209        -->
210        <service
210-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
211            android:name="com.google.firebase.messaging.FirebaseMessagingService"
211-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
212            android:directBootAware="true"
212-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
213            android:exported="false" >
213-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
214            <intent-filter android:priority="-500" >
214-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:73:13-75:29
215                <action android:name="com.google.firebase.MESSAGING_EVENT" />
215-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:74:17-78
215-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:74:25-75
216            </intent-filter>
217        </service>
218        <service
218-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
219            android:name="com.google.firebase.components.ComponentDiscoveryService"
219-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
220            android:directBootAware="true"
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
221            android:exported="false" >
221-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
222            <meta-data
222-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
223                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
223-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
225            <meta-data
225-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
226                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
226-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
228            <meta-data
228-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
229                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
229-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
231            <meta-data
231-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
232                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
232-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
234            <meta-data
234-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
235                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
235-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
236                android:value="com.google.firebase.components.ComponentRegistrar" />
236-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
237            <meta-data
237-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
238                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
238-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
239                android:value="com.google.firebase.components.ComponentRegistrar" />
239-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
240            <meta-data
240-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
241                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
241-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
242                android:value="com.google.firebase.components.ComponentRegistrar" />
242-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
243        </service>
244
245        <activity
245-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
246            android:name="com.google.android.gms.common.api.GoogleApiActivity"
246-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
247            android:exported="false"
247-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
248            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
248-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
249        <activity
249-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
250            android:name="com.facebook.ads.AudienceNetworkActivity"
250-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
251            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
251-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
252            android:exported="false"
252-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
253            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
253-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
254
255        <provider
255-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
256            android:name="com.facebook.ads.AudienceNetworkContentProvider"
256-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
257            android:authorities="com.official.fivegfastvpn.AudienceNetworkContentProvider"
257-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
258            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
258-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
259        <activity
259-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
260            android:name="com.google.android.gms.ads.AdActivity"
260-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
261            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
261-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
262            android:exported="false"
262-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
263            android:theme="@android:style/Theme.Translucent" />
263-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
264
265        <provider
265-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
266            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
266-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
267            android:authorities="com.official.fivegfastvpn.mobileadsinitprovider"
267-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
268            android:exported="false"
268-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
269            android:initOrder="100" />
269-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
270
271        <service
271-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
272            android:name="com.google.android.gms.ads.AdService"
272-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
273            android:enabled="true"
273-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
274            android:exported="false" />
274-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
275
276        <activity
276-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
277            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
277-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
278            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
278-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
279            android:exported="false" />
279-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
280        <activity
280-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
281            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
281-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
282            android:excludeFromRecents="true"
282-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
283            android:exported="false"
283-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
284            android:launchMode="singleTask"
284-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
285            android:taskAffinity=""
285-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
286            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
286-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
287
288        <meta-data
288-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
289            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
289-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
290            android:value="true" />
290-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
291        <meta-data
291-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
292            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
292-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
293            android:value="true" />
293-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
294
295        <provider
295-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
296            android:name="com.google.firebase.provider.FirebaseInitProvider"
296-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
297            android:authorities="com.official.fivegfastvpn.firebaseinitprovider"
297-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
298            android:directBootAware="true"
298-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
299            android:exported="false"
299-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
300            android:initOrder="100" />
300-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
301        <provider
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
302            android:name="androidx.startup.InitializationProvider"
302-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
303            android:authorities="com.official.fivegfastvpn.androidx-startup"
303-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
304            android:exported="false" >
304-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
305            <meta-data
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
306                android:name="androidx.work.WorkManagerInitializer"
306-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
307                android:value="androidx.startup" />
307-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
308            <meta-data
308-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
309                android:name="androidx.emoji2.text.EmojiCompatInitializer"
309-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
310                android:value="androidx.startup" />
310-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
311            <meta-data
311-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
312                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
312-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
313                android:value="androidx.startup" />
313-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
314            <meta-data
314-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
315                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
315-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
316                android:value="androidx.startup" />
316-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
317        </provider>
318
319        <service
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
320            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
322            android:enabled="@bool/enable_system_alarm_service_default"
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
323            android:exported="false" />
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
324        <service
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
325            android:name="androidx.work.impl.background.systemjob.SystemJobService"
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
326            android:directBootAware="false"
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
327            android:enabled="@bool/enable_system_job_service_default"
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
328            android:exported="true"
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
329            android:permission="android.permission.BIND_JOB_SERVICE" />
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
330        <service
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
331            android:name="androidx.work.impl.foreground.SystemForegroundService"
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
333            android:enabled="@bool/enable_system_foreground_service_default"
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
334            android:exported="false" />
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
335
336        <receiver
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
337            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
339            android:enabled="true"
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
340            android:exported="false" />
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
341        <receiver
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
342            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
343            android:directBootAware="false"
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
344            android:enabled="false"
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
345            android:exported="false" >
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
346            <intent-filter>
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
347                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
347-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
347-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
348                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
349            </intent-filter>
350        </receiver>
351        <receiver
351-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
352            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
353            android:directBootAware="false"
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
354            android:enabled="false"
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
355            android:exported="false" >
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
356            <intent-filter>
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
357                <action android:name="android.intent.action.BATTERY_OKAY" />
357-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
357-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
358                <action android:name="android.intent.action.BATTERY_LOW" />
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
359            </intent-filter>
360        </receiver>
361        <receiver
361-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
362            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
363            android:directBootAware="false"
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
364            android:enabled="false"
364-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
365            android:exported="false" >
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
366            <intent-filter>
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
367                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
367-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
367-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
368                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
369            </intent-filter>
370        </receiver>
371        <receiver
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
372            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
373            android:directBootAware="false"
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
374            android:enabled="false"
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
375            android:exported="false" >
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
376            <intent-filter>
376-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
377                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
377-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
377-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
378            </intent-filter>
379        </receiver>
380        <receiver
380-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
381            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
382            android:directBootAware="false"
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
383            android:enabled="false"
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
384            android:exported="false" >
384-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
385            <intent-filter>
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
386                <action android:name="android.intent.action.BOOT_COMPLETED" />
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
387                <action android:name="android.intent.action.TIME_SET" />
387-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
387-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
388                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
389            </intent-filter>
390        </receiver>
391        <receiver
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
392            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
392-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
393            android:directBootAware="false"
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
394            android:enabled="@bool/enable_system_alarm_service_default"
394-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
395            android:exported="false" >
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
396            <intent-filter>
396-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
397                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
397-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
397-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
398            </intent-filter>
399        </receiver>
400        <receiver
400-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
401            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
401-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
402            android:directBootAware="false"
402-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
403            android:enabled="true"
403-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
404            android:exported="true"
404-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
405            android:permission="android.permission.DUMP" >
405-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
406            <intent-filter>
406-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
407                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
407-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
407-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
408            </intent-filter>
409        </receiver>
410
411        <service
411-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
412            android:name="androidx.room.MultiInstanceInvalidationService"
412-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
413            android:directBootAware="true"
413-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
414            android:exported="false" />
414-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
415
416        <uses-library
416-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
417            android:name="androidx.window.extensions"
417-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
418            android:required="false" />
418-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
419        <uses-library
419-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
420            android:name="androidx.window.sidecar"
420-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
421            android:required="false" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
421-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
422        <provider
422-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:12:9-15:40
423            android:name="io.sentry.android.core.SentryInitProvider"
423-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:13:13-69
424            android:authorities="com.official.fivegfastvpn.SentryInitProvider"
424-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:14:13-70
425            android:exported="false" />
425-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:15:13-37
426        <provider
426-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:16:9-20:39
427            android:name="io.sentry.android.core.SentryPerformanceProvider"
427-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:17:13-76
428            android:authorities="com.official.fivegfastvpn.SentryPerformanceProvider"
428-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:18:13-77
429            android:exported="false"
429-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:19:13-37
430            android:initOrder="200" />
430-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:20:13-36
431
432        <uses-library
432-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
433            android:name="android.ext.adservices"
433-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
434            android:required="false" />
434-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
435
436        <meta-data
436-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
437            android:name="com.google.android.gms.version"
437-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
438            android:value="@integer/google_play_services_version" />
438-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
439
440        <receiver
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
441            android:name="androidx.profileinstaller.ProfileInstallReceiver"
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
442            android:directBootAware="false"
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
443            android:enabled="true"
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
444            android:exported="true"
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
445            android:permission="android.permission.DUMP" >
445-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
446            <intent-filter>
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
447                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
448            </intent-filter>
449            <intent-filter>
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
450                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
451            </intent-filter>
452            <intent-filter>
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
453                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
454            </intent-filter>
455            <intent-filter>
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
456                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
457            </intent-filter>
458        </receiver>
459
460        <service
460-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
461            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
461-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
462            android:exported="false" >
462-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
463            <meta-data
463-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
464                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
464-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
465                android:value="cct" />
465-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
466        </service>
467        <service
467-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
468            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
468-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
469            android:exported="false"
469-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
470            android:permission="android.permission.BIND_JOB_SERVICE" >
470-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
471        </service>
472
473        <receiver
473-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
474            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
474-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
475            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
475-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
476        <activity
476-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
477            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
477-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
478            android:exported="false"
478-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
479            android:stateNotNeeded="true"
479-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
480            android:theme="@style/Theme.PlayCore.Transparent" />
480-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
481    </application>
482
483</manifest>
