// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogContactUsBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button contactCancelButton;

  @NonNull
  public final ImageView contactIcon;

  @NonNull
  public final Button contactSendButton;

  @NonNull
  public final CardView emailCard;

  @NonNull
  public final TextInputEditText emailInput;

  @NonNull
  public final TextInputLayout emailInputLayout;

  @NonNull
  public final TextInputEditText messageInput;

  @NonNull
  public final TextInputLayout messageInputLayout;

  @NonNull
  public final TextInputEditText nameInput;

  @NonNull
  public final TextInputLayout nameInputLayout;

  private DialogContactUsBinding(@NonNull CardView rootView, @NonNull Button contactCancelButton,
      @NonNull ImageView contactIcon, @NonNull Button contactSendButton,
      @NonNull CardView emailCard, @NonNull TextInputEditText emailInput,
      @NonNull TextInputLayout emailInputLayout, @NonNull TextInputEditText messageInput,
      @NonNull TextInputLayout messageInputLayout, @NonNull TextInputEditText nameInput,
      @NonNull TextInputLayout nameInputLayout) {
    this.rootView = rootView;
    this.contactCancelButton = contactCancelButton;
    this.contactIcon = contactIcon;
    this.contactSendButton = contactSendButton;
    this.emailCard = emailCard;
    this.emailInput = emailInput;
    this.emailInputLayout = emailInputLayout;
    this.messageInput = messageInput;
    this.messageInputLayout = messageInputLayout;
    this.nameInput = nameInput;
    this.nameInputLayout = nameInputLayout;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogContactUsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogContactUsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_contact_us, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogContactUsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contact_cancel_button;
      Button contactCancelButton = ViewBindings.findChildViewById(rootView, id);
      if (contactCancelButton == null) {
        break missingId;
      }

      id = R.id.contact_icon;
      ImageView contactIcon = ViewBindings.findChildViewById(rootView, id);
      if (contactIcon == null) {
        break missingId;
      }

      id = R.id.contact_send_button;
      Button contactSendButton = ViewBindings.findChildViewById(rootView, id);
      if (contactSendButton == null) {
        break missingId;
      }

      id = R.id.email_card;
      CardView emailCard = ViewBindings.findChildViewById(rootView, id);
      if (emailCard == null) {
        break missingId;
      }

      id = R.id.email_input;
      TextInputEditText emailInput = ViewBindings.findChildViewById(rootView, id);
      if (emailInput == null) {
        break missingId;
      }

      id = R.id.email_input_layout;
      TextInputLayout emailInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (emailInputLayout == null) {
        break missingId;
      }

      id = R.id.message_input;
      TextInputEditText messageInput = ViewBindings.findChildViewById(rootView, id);
      if (messageInput == null) {
        break missingId;
      }

      id = R.id.message_input_layout;
      TextInputLayout messageInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (messageInputLayout == null) {
        break missingId;
      }

      id = R.id.name_input;
      TextInputEditText nameInput = ViewBindings.findChildViewById(rootView, id);
      if (nameInput == null) {
        break missingId;
      }

      id = R.id.name_input_layout;
      TextInputLayout nameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (nameInputLayout == null) {
        break missingId;
      }

      return new DialogContactUsBinding((CardView) rootView, contactCancelButton, contactIcon,
          contactSendButton, emailCard, emailInput, emailInputLayout, messageInput,
          messageInputLayout, nameInput, nameInputLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
