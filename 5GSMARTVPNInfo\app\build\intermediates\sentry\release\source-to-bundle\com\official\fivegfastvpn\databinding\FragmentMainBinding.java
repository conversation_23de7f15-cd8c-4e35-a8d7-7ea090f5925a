// Generated by data binding compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import com.airbnb.lottie.LottieAnimationView;
import com.official.fivegfastvpn.R;
import java.lang.Deprecated;
import java.lang.Object;

public abstract class FragmentMainBinding extends ViewDataBinding {
  @NonNull
  public final LottieAnimationView btnConnect;

  @NonNull
  public final Button btnRenew;

  @NonNull
  public final TextView byteInTv;

  @NonNull
  public final TextView byteOutTv;

  @NonNull
  public final ImageView category;

  @NonNull
  public final ImageView chevronRight;

  @NonNull
  public final LinearLayout contime1;

  @NonNull
  public final TextView countryName;

  @NonNull
  public final RelativeLayout currentConnectionLayout;

  @NonNull
  public final TextView durationTv;

  @NonNull
  public final Button extraTime;

  @NonNull
  public final TextView ipAddress;

  @NonNull
  public final TextView ipTv;

  @NonNull
  public final TextView logTv;

  @NonNull
  public final ImageView notification;

  @NonNull
  public final LinearLayout pre;

  @NonNull
  public final ImageView premium;

  @NonNull
  public final TextView protectionStatus;

  @NonNull
  public final LinearLayout purchaseLayout;

  @NonNull
  public final LinearLayout renewButtonLayout;

  @NonNull
  public final ImageView selectedServerIcon;

  @NonNull
  public final LinearLayout statsLayout;

  @NonNull
  public final TextView text;

  @NonNull
  public final RelativeLayout toolbar;

  protected FragmentMainBinding(Object _bindingComponent, View _root, int _localFieldCount,
      LottieAnimationView btnConnect, Button btnRenew, TextView byteInTv, TextView byteOutTv,
      ImageView category, ImageView chevronRight, LinearLayout contime1, TextView countryName,
      RelativeLayout currentConnectionLayout, TextView durationTv, Button extraTime,
      TextView ipAddress, TextView ipTv, TextView logTv, ImageView notification, LinearLayout pre,
      ImageView premium, TextView protectionStatus, LinearLayout purchaseLayout,
      LinearLayout renewButtonLayout, ImageView selectedServerIcon, LinearLayout statsLayout,
      TextView text, RelativeLayout toolbar) {
    super(_bindingComponent, _root, _localFieldCount);
    this.btnConnect = btnConnect;
    this.btnRenew = btnRenew;
    this.byteInTv = byteInTv;
    this.byteOutTv = byteOutTv;
    this.category = category;
    this.chevronRight = chevronRight;
    this.contime1 = contime1;
    this.countryName = countryName;
    this.currentConnectionLayout = currentConnectionLayout;
    this.durationTv = durationTv;
    this.extraTime = extraTime;
    this.ipAddress = ipAddress;
    this.ipTv = ipTv;
    this.logTv = logTv;
    this.notification = notification;
    this.pre = pre;
    this.premium = premium;
    this.protectionStatus = protectionStatus;
    this.purchaseLayout = purchaseLayout;
    this.renewButtonLayout = renewButtonLayout;
    this.selectedServerIcon = selectedServerIcon;
    this.statsLayout = statsLayout;
    this.text = text;
    this.toolbar = toolbar;
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup root, boolean attachToRoot) {
    return inflate(inflater, root, attachToRoot, DataBindingUtil.getDefaultComponent());
  }

  /**
   * This method receives DataBindingComponent instance as type Object instead of
   * type DataBindingComponent to avoid causing too many compilation errors if
   * compilation fails for another reason.
   * https://issuetracker.google.com/issues/116541301
   * @Deprecated Use DataBindingUtil.inflate(inflater, R.layout.fragment_main, root, attachToRoot, component)
   */
  @NonNull
  @Deprecated
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup root, boolean attachToRoot, @Nullable Object component) {
    return ViewDataBinding.<FragmentMainBinding>inflateInternal(inflater, R.layout.fragment_main, root, attachToRoot, component);
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, DataBindingUtil.getDefaultComponent());
  }

  /**
   * This method receives DataBindingComponent instance as type Object instead of
   * type DataBindingComponent to avoid causing too many compilation errors if
   * compilation fails for another reason.
   * https://issuetracker.google.com/issues/116541301
   * @Deprecated Use DataBindingUtil.inflate(inflater, R.layout.fragment_main, null, false, component)
   */
  @NonNull
  @Deprecated
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable Object component) {
    return ViewDataBinding.<FragmentMainBinding>inflateInternal(inflater, R.layout.fragment_main, null, false, component);
  }

  public static FragmentMainBinding bind(@NonNull View view) {
    return bind(view, DataBindingUtil.getDefaultComponent());
  }

  /**
   * This method receives DataBindingComponent instance as type Object instead of
   * type DataBindingComponent to avoid causing too many compilation errors if
   * compilation fails for another reason.
   * https://issuetracker.google.com/issues/116541301
   * @Deprecated Use DataBindingUtil.bind(view, component)
   */
  @Deprecated
  public static FragmentMainBinding bind(@NonNull View view, @Nullable Object component) {
    return (FragmentMainBinding)bind(component, view, R.layout.fragment_main);
  }
}
