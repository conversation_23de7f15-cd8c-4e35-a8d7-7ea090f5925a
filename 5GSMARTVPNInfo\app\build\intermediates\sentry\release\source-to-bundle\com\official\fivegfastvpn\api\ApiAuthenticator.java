package com.official.fivegfastvpn.api;

import android.util.Log;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * API Authentication Helper for 5G Smart VPN
 * Handles HMAC-SHA256 signature generation for API requests
 */
public class ApiAuthenticator {
    private static final String TAG = "ApiAuthenticator";
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    
    /**
     * Generate HMAC-SHA256 signature for API authentication
     * @param timestamp Current timestamp in seconds
     * @param secretKey API secret key
     * @return HMAC signature as hex string
     */
    public static String generateSignature(String timestamp, String secretKey) {
        try {
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                secretKey.getBytes(StandardCharsets.UTF_8), 
                HMAC_ALGORITHM
            );
            mac.init(secretKeySpec);
            
            byte[] hashBytes = mac.doFinal(timestamp.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
            
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            Log.e(TAG, "Error generating HMAC signature", e);
            return null;
        }
    }
    
    /**
     * Get current timestamp in seconds
     * @return Current timestamp as string
     */
    public static String getCurrentTimestamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }
    
    /**
     * Convert byte array to hex string
     * @param bytes Byte array to convert
     * @return Hex string representation
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * Build authenticated URL with timestamp and signature
     * @param baseUrl Base API URL
     * @param packageName App package name (for config endpoint)
     * @return Authenticated URL with parameters
     */
    public static String buildAuthenticatedUrl(String baseUrl, String packageName) {
        String timestamp = getCurrentTimestamp();
        String signature = generateSignature(timestamp, Const.API_SECRET_KEY);
        
        if (signature == null) {
            Log.e(TAG, "Failed to generate signature");
            return null;
        }
        
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        
        // Add package parameter if provided
        if (packageName != null && !packageName.isEmpty()) {
            urlBuilder.append("?pkg=").append(packageName);
            urlBuilder.append("&timestamp=").append(timestamp);
            urlBuilder.append("&signature=").append(signature);
        } else {
            urlBuilder.append("?timestamp=").append(timestamp);
            urlBuilder.append("&signature=").append(signature);
        }
        
        return urlBuilder.toString();
    }
    
    /**
     * Build authenticated URL without package parameter
     * @param baseUrl Base API URL
     * @return Authenticated URL with timestamp and signature
     */
    public static String buildAuthenticatedUrl(String baseUrl) {
        return buildAuthenticatedUrl(baseUrl, null);
    }
    
    /**
     * Add authentication parameters to existing URL
     * @param url Existing URL (may already have parameters)
     * @return URL with authentication parameters added
     */
    public static String addAuthParams(String url) {
        String timestamp = getCurrentTimestamp();
        String signature = generateSignature(timestamp, Const.API_SECRET_KEY);
        
        if (signature == null) {
            Log.e(TAG, "Failed to generate signature");
            return url;
        }
        
        String separator = url.contains("?") ? "&" : "?";
        return url + separator + "timestamp=" + timestamp + "&signature=" + signature;
    }
    
    /**
     * Validate if API secret key is properly configured
     * @return true if API key is configured, false otherwise
     */
    public static boolean isApiKeyConfigured() {
        return !Const.API_SECRET_KEY.equals("your-secret-api-key-here") && 
               !Const.API_SECRET_KEY.isEmpty();
    }
}
