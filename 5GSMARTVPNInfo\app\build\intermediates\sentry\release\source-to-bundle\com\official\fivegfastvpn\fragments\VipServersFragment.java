package com.official.fivegfastvpn.fragments;

//Developer :--<PERSON><PERSON>er

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.activity.ServersActivity;
import com.official.fivegfastvpn.adapter.VipServerAdapter;
import com.official.fivegfastvpn.api.Const;
import com.official.fivegfastvpn.model.Server;
import com.official.fivegfastvpn.utils.Pref;
import com.official.fivegfastvpn.utils.Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class VipServersFragment extends Fragment implements VipServerAdapter.OnSelectListener {


    RecyclerView recyclerView;
    VipServerAdapter serverAdapter;
    View layout;

    private List<Server> originalServers; // Store the original list of servers
    private SearchView searchView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        layout = inflater.inflate(R.layout.fragment_vip_server, container, false);

        ServersActivity.resume = true;

        recyclerView = layout.findViewById(R.id.recycler_vip);

        serverAdapter = new VipServerAdapter(getActivity());

        serverAdapter.setOnSelectListener(this);
        recyclerView.setAdapter(serverAdapter);

        loadServers();
        searchView = layout.findViewById(R.id.searchView);
        setupSearchView();

        return layout;
    }

    private void setupSearchView() {
        searchView = layout.findViewById(R.id.searchView);
        if (searchView != null) {
            searchView.setQueryHint(getString(R.string.search));
            searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextSubmit(String query) {
                    return false;
                }

                @Override
                public boolean onQueryTextChange(String newText) {
                    filterList(newText);
                    return true;
                }
            });
        }
    }


    private void loadServers() {
        originalServers = new ArrayList<>(); // Initialize the original servers list
        ArrayList<Server> servers = new ArrayList<>();
        try {
            JSONArray jsonArray = new JSONArray(Const.SERVERS);
            for (int i = 0; i < jsonArray.length(); i++) {

                JSONObject object = (JSONObject) jsonArray.get(i);

                if (object.getString("type").contains("0")) {

                    Log.d("dddddddddddd", "loadServers: " + Utils.imgUrl("flag/", object.getString("flagURL")));

                    servers.add(new Server(object.getString("name"),
                            Utils.imgUrl("flag/", object.getString("flagURL")),
                            object.getString("configFile"),
                            object.getString("username"),
                            object.getString("password")
                    ));
                    originalServers.add(new Server(object.getString("name"),
                            Utils.imgUrl("flag/", object.getString("flagURL")),
                            object.getString("configFile"),
                            object.getString("username"),
                            object.getString("password")
                    ));
                    Log.v("Servers", object.getString("configFile"));

                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        serverAdapter.setData(servers);
    }

    private void filterList(String query) {
        ArrayList<Server> filteredServers = new ArrayList<>();
        if (originalServers != null) {
            for (Server server : originalServers) {
                if (server.getCountry().toLowerCase().contains(query.toLowerCase())) {
                    filteredServers.add(server);
                }
            }
        }
        serverAdapter.setData(filteredServers);
    }


    @Override
    public void onSelected(Server server) {
        if (getActivity() != null) {
            Intent mIntent = new Intent();
            mIntent.putExtra("server", server);

            Pref pref = new Pref(getActivity());
            pref.saveServer(server);

            getActivity().setResult(getActivity().RESULT_OK, mIntent);
            getActivity().finish();
        }
    }
}