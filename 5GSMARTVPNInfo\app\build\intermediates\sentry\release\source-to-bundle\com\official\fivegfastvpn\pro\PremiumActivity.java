package com.official.fivegfastvpn.pro;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageButton;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.android.billingclient.api.AcknowledgePurchaseParams;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.QueryPurchasesParams;
import com.google.android.material.button.MaterialButton;
import com.official.fivegfastvpn.MainActivity;
import com.official.fivegfastvpn.R;

import java.util.ArrayList;
import java.util.List;

public class PremiumActivity extends AppCompatActivity {
    private static final String TAG = "PremiumActivity";
    
    private BillingClient billingClient;
    private ProductDetails selectedProduct;
    private CardView annualPlan;
    private CardView monthlyPlan;
    private MaterialButton subscribeButton;
    private boolean isAnnualPlanSelected = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.pro_activity_premium);

        initializeViews();
        setupBillingClient();
        setupClickListeners();
    }

    private void initializeViews() {
        ImageButton closeButton = findViewById(R.id.btn_close);
        annualPlan = findViewById(R.id.annual_plan);
        monthlyPlan = findViewById(R.id.monthly_plan);
        subscribeButton = findViewById(R.id.btn_subscribe);

        closeButton.setOnClickListener(v -> finish());
    }

    private void setupClickListeners() {
        annualPlan.setOnClickListener(v -> {
            isAnnualPlanSelected = true;
            updatePlanSelection();
        });

        monthlyPlan.setOnClickListener(v -> {
            isAnnualPlanSelected = false;
            updatePlanSelection();
        });

        subscribeButton.setOnClickListener(v -> {
            if (selectedProduct != null) {
                launchPurchaseFlow(selectedProduct);
            } else {
                Toast.makeText(this, "Please wait while we load the subscription details", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void updatePlanSelection() {
        annualPlan.setCardElevation(isAnnualPlanSelected ? 8f : 2f);
        monthlyPlan.setCardElevation(isAnnualPlanSelected ? 2f : 8f);
        
        // Update selected product based on plan
        for (ProductDetails product : productDetailsList) {
            String productId = product.getProductId();
            if ((isAnnualPlanSelected && productId.equals(ProConfig.all_yearly_id)) ||
                (!isAnnualPlanSelected && productId.equals(ProConfig.all_month_id))) {
                selectedProduct = product;
                break;
            }
        }
    }

    private void setupBillingClient() {
        billingClient = BillingClient.newBuilder(this)
                .enablePendingPurchases()
                .setListener((billingResult, purchases) -> {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && purchases != null) {
                        for (Purchase purchase : purchases) {
                            handlePurchase(purchase);
                        }
                    }
                })
                .build();

        establishConnection();
    }

    private final List<ProductDetails> productDetailsList = new ArrayList<>();

    private void establishConnection() {
        billingClient.startConnection(new BillingClientStateListener() {
            @Override
            public void onBillingSetupFinished(@NonNull BillingResult billingResult) {
                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    queryProductDetails();
                }
            }

            @Override
            public void onBillingServiceDisconnected() {
                establishConnection();
            }
        });
    }

    private void queryProductDetails() {
        List<QueryProductDetailsParams.Product> productList = new ArrayList<>();
        productList.add(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(ProConfig.all_month_id)
                .setProductType(BillingClient.ProductType.SUBS)
                .build());
        productList.add(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(ProConfig.all_yearly_id)
                .setProductType(BillingClient.ProductType.SUBS)
                .build());

        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();

        billingClient.queryProductDetailsAsync(params, (billingResult, productDetailsList) -> {
            if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                this.productDetailsList.clear();
                this.productDetailsList.addAll(productDetailsList);
                
                // Set initial selected product
                updatePlanSelection();
            }
        });
    }

    private void launchPurchaseFlow(ProductDetails productDetails) {
        if (productDetails.getSubscriptionOfferDetails() == null || 
            productDetails.getSubscriptionOfferDetails().isEmpty()) {
            Toast.makeText(this, "Subscription details not available", Toast.LENGTH_SHORT).show();
            return;
        }

        List<BillingFlowParams.ProductDetailsParams> productDetailsParamsList = new ArrayList<>();
        productDetailsParamsList.add(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                        .setProductDetails(productDetails)
                        .setOfferToken(productDetails.getSubscriptionOfferDetails().get(0).getOfferToken())
                        .build()
        );

        BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .build();

        billingClient.launchBillingFlow(this, billingFlowParams);
    }

    private void handlePurchase(Purchase purchase) {
        if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
            if (!purchase.isAcknowledged()) {
                AcknowledgePurchaseParams acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                        .setPurchaseToken(purchase.getPurchaseToken())
                        .build();

                billingClient.acknowledgePurchase(acknowledgePurchaseParams, billingResult -> {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        Toast.makeText(PremiumActivity.this, "Subscription activated, Enjoy!", Toast.LENGTH_SHORT).show();
                        ProConfig.setPremium(true, PremiumActivity.this);
                        startActivity(new Intent(this, MainActivity.class));
                        finish();
                    }
                });
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        billingClient.queryPurchasesAsync(
                QueryPurchasesParams.newBuilder()
                        .setProductType(BillingClient.ProductType.SUBS)
                        .build(),
                (billingResult, list) -> {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        for (Purchase purchase : list) {
                            if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED && 
                                !purchase.isAcknowledged()) {
                                handlePurchase(purchase);
                            }
                        }
                    }
                });
    }
}