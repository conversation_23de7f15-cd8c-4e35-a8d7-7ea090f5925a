package com.official.fivegfastvpn.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.official.fivegfastvpn.MainActivity;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.activity.VpnWarningActivity;
import com.official.fivegfastvpn.pro.ProConfig;
import com.official.fivegfastvpn.utils.Pref;

/**
 * Background service to keep VPN timer running even when app is closed
 */
public class VpnTimerService extends Service {
    
    private static final String TAG = "VpnTimerService";
    private static final int NOTIFICATION_ID = 2001;
    private static final String CHANNEL_ID = "vpn_timer_channel";
    private static final String WARNING_CHANNEL_ID = "vpn_warning_channel";

    // CRITICAL FIX: Notification spam prevention
    private boolean isForegroundStarted = false;
    private String lastNotificationText = "";
    private long lastNotificationUpdate = 0;
    private static final long NOTIFICATION_UPDATE_THROTTLE_MS = 5000; // Update max every 5 seconds
    
    private Handler timerHandler;
    private Runnable timerRunnable;
    private long timerStartTime;
    private int remainingTimeInSeconds;
    private boolean isPremium;
    private Pref pref;
    private boolean hasShownWarning = false;

    // Service health check
    private Handler healthCheckHandler;
    private Runnable healthCheckRunnable;
    
    @Override
    public void onCreate() {
        super.onCreate();
        timerHandler = new Handler();
        healthCheckHandler = new Handler();
        pref = new Pref(this);
        createNotificationChannels();
        startHealthCheck();
        Log.d(TAG, "VpnTimerService created");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            // Check if this is a timer extension request
            boolean isExtension = intent.getBooleanExtra("extend_timer", false);
            int additionalSeconds = intent.getIntExtra("additional_seconds", 0);

            if (isExtension && additionalSeconds > 0) {
                // CRITICAL FIX: Extend existing timer instead of restarting
                Log.d(TAG, "TIMER DEBUG: Received timer extension request");
                Log.d(TAG, "TIMER DEBUG: Current remainingTimeInSeconds = " + remainingTimeInSeconds);
                Log.d(TAG, "TIMER DEBUG: Additional seconds to add = " + additionalSeconds);
                Log.d(TAG, "TIMER DEBUG: Extending timer by " + additionalSeconds + " seconds");
                extendTimer(additionalSeconds);
                return START_STICKY;
            }

            // Normal timer start/restart logic
            long newTimerStartTime = intent.getLongExtra("timer_start_time", System.currentTimeMillis());
            int newRemainingTime = intent.getIntExtra("remaining_time", 0);
            boolean newIsPremium = intent.getBooleanExtra("is_premium", false);

            Log.d(TAG, "TIMER DEBUG: VpnTimerService.onStartCommand called");
            Log.d(TAG, "TIMER DEBUG: Received timerStartTime = " + newTimerStartTime);
            Log.d(TAG, "TIMER DEBUG: Received remainingTimeInSeconds = " + newRemainingTime);
            Log.d(TAG, "TIMER DEBUG: Received isPremium = " + newIsPremium);

            if (newRemainingTime <= 0 && !newIsPremium) {
                Log.e(TAG, "TIMER DEBUG: ERROR - Received 0 or negative remaining time for non-premium user!");
                Log.e(TAG, "TIMER DEBUG: This will cause immediate VPN disconnection!");
                // Don't start the timer if we have invalid time
                return START_NOT_STICKY;
            }

            // Update timer values
            timerStartTime = newTimerStartTime;
            remainingTimeInSeconds = newRemainingTime;
            isPremium = newIsPremium;
            hasShownWarning = false; // Reset warning flag for new timer

            Log.d(TAG, "Timer service started - Premium: " + isPremium +
                      ", Remaining: " + remainingTimeInSeconds + " seconds");

            // Stop existing timer before starting new one
            stopTimer();

            startForegroundService();
            startTimer();
        } else {
            Log.e(TAG, "TIMER DEBUG: onStartCommand called with null intent!");
        }

        return START_STICKY; // Restart service if killed
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        stopTimer();
        stopHealthCheck();

        // CRITICAL FIX: Clean up notification tracking state
        isForegroundStarted = false;
        lastNotificationText = "";
        lastNotificationUpdate = 0;

        // Clear any remaining notifications
        try {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.cancel(NOTIFICATION_ID);
                notificationManager.cancel(9999); // Warning notification
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing notifications on destroy", e);
        }

        Log.d(TAG, "VpnTimerService destroyed and notifications cleared");
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        Log.d(TAG, "Task removed - but service should continue running");

        // Check if VPN is still connected and timer should continue
        if (shouldContinueRunning()) {
            Log.d(TAG, "VPN still connected, keeping timer service alive");
            // Restart the service to ensure it continues running
            Intent restartIntent = new Intent(getApplicationContext(), VpnTimerService.class);
            restartIntent.putExtra("timer_start_time", timerStartTime);
            restartIntent.putExtra("remaining_time", remainingTimeInSeconds);
            restartIntent.putExtra("is_premium", isPremium);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(restartIntent);
            } else {
                startService(restartIntent);
            }
        } else {
            Log.d(TAG, "VPN not connected, allowing service to stop");
            stopSelf();
        }
    }

    private boolean shouldContinueRunning() {
        // Check if VPN is still connected by checking preferences
        try {
            Pref pref = new Pref(this);
            boolean isVpnConnected = pref.getVpnConnectionState();
            Log.d(TAG, "VPN connection state: " + isVpnConnected);
            return isVpnConnected && (isPremium || remainingTimeInSeconds > 0);
        } catch (Exception e) {
            Log.e(TAG, "Error checking VPN state", e);
            return false;
        }
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);

            // Timer notification channel - SILENT and NON-INTRUSIVE
            NotificationChannel timerChannel = new NotificationChannel(
                CHANNEL_ID,
                "VPN Timer",
                NotificationManager.IMPORTANCE_LOW  // Changed from DEFAULT to LOW for silent updates
            );
            timerChannel.setDescription("VPN timer background service - silent updates");
            timerChannel.setShowBadge(false);  // Disable badge to reduce visual noise
            timerChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            timerChannel.enableLights(false);
            timerChannel.enableVibration(false);
            timerChannel.setSound(null, null);  // Explicitly disable sound
            timerChannel.setBypassDnd(false);   // Don't bypass Do Not Disturb
            notificationManager.createNotificationChannel(timerChannel);

            // Warning notification channel - KEEP HIGH IMPORTANCE for important alerts
            NotificationChannel warningChannel = new NotificationChannel(
                WARNING_CHANNEL_ID,
                "VPN Warning",
                NotificationManager.IMPORTANCE_HIGH  // Keep HIGH for 1-minute warning and important events
            );
            warningChannel.setDescription("VPN time warning notifications - important alerts only");
            warningChannel.enableVibration(true);
            warningChannel.setLightColor(android.graphics.Color.RED);
            notificationManager.createNotificationChannel(warningChannel);
        }
    }

    private void startForegroundService() {
        // CRITICAL FIX: Prevent duplicate foreground service starts
        if (isForegroundStarted) {
            Log.d(TAG, "Foreground service already started, skipping duplicate start");
            return;
        }

        // Create silent notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "VPN Timer Channel",
                    NotificationManager.IMPORTANCE_LOW // LOW = silent (no sound or vibration)
            );
            channel.setDescription("Channel for VPN timer updates");
            channel.setSound(null, null); // Disable sound
            channel.enableLights(false);
            channel.enableVibration(false);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_MUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
        }

        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this, CHANNEL_ID);
        } else {
            builder = new Notification.Builder(this);
        }

        String contentText = isPremium ? "VPN timer running" : "VPN timer: " + formatTime(remainingTimeInSeconds);

        Notification.Builder notificationBuilder = builder
                .setContentTitle("VPN Timer Active")
                .setContentText(contentText)
                .setSmallIcon(R.drawable.fivegsmartvpn)
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .setAutoCancel(false)
                .setPriority(Notification.PRIORITY_LOW)
                .setCategory(Notification.CATEGORY_SERVICE)
                .setVisibility(Notification.VISIBILITY_PUBLIC)
                .setOnlyAlertOnce(true)
                .setSound(null); // Explicitly disable sound for API < 26

        Notification notification = notificationBuilder.build();

        startForeground(NOTIFICATION_ID, notification);
        isForegroundStarted = true;
        lastNotificationText = contentText;
        lastNotificationUpdate = System.currentTimeMillis();
        Log.d(TAG, "Foreground service started with notification: " + contentText);
    }


    private void startTimer() {
        if (timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }
        
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                if (isPremium) {
                    // For premium users, just track elapsed time
                    long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                    int elapsedSeconds = (int) (elapsedMillis / 1000);
                    
                    // Save state periodically
                    pref.saveVpnTimerState(timerStartTime, elapsedSeconds, true, 0);

                    // CRITICAL FIX: Update notification less frequently to prevent spam
                    // Only update notification every 10 seconds for premium users
                    if (elapsedSeconds % 10 == 0) {
                        updateNotification("VPN timer: " + formatElapsedTime(elapsedSeconds));
                    }

                    // Broadcast timer update (keep this frequent for UI updates)
                    broadcastTimerUpdate(elapsedSeconds, true);
                    
                    timerHandler.postDelayed(this, 1000);
                } else {
                    // For non-premium users, countdown timer
                    if (remainingTimeInSeconds > 0) {
                        remainingTimeInSeconds--;
                        
                        // Save state
                        long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                        int elapsedSeconds = (int) (elapsedMillis / 1000);
                        pref.saveVpnTimerState(timerStartTime, elapsedSeconds, false, remainingTimeInSeconds);
                        
                        // CRITICAL FIX: Update notification less frequently to prevent spam
                        // Only update notification every 10 seconds for non-premium users
                        if (remainingTimeInSeconds % 10 == 0) {
                            updateNotification("VPN timer: " + formatTime(remainingTimeInSeconds));
                        }
                        
                        // Check for 1-minute warning
                        if (remainingTimeInSeconds == 60 && !hasShownWarning) {
                            showOneMinuteWarningNotification();
                            hasShownWarning = true;
                        }
                        
                        // Broadcast timer update
                        broadcastTimerUpdate(remainingTimeInSeconds, false);
                        
                        timerHandler.postDelayed(this, 1000);
                    } else {
                        // Time expired - disconnect VPN
                        Log.d(TAG, "Timer expired - disconnecting VPN and broadcasting");

                        // CRITICAL FIX: Directly disconnect VPN from service
                        disconnectVpnDirectly();

                        // Also broadcast for any active UI components
                        broadcastVpnDisconnect();

                        // Stop the service
                        stopSelf();
                    }
                }
            }
        };
        
        timerHandler.post(timerRunnable);
    }
    
    private void stopTimer() {
        if (timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
            timerRunnable = null;
        }
    }

    /**
     * CRITICAL FIX: Extend the current timer by adding additional seconds
     * This prevents timer reset when watching ads for more time
     */
    private void extendTimer(int additionalSeconds) {
        try {
            if (isPremium) {
                Log.d(TAG, "Cannot extend timer for premium users (unlimited time)");
                return;
            }

            if (additionalSeconds <= 0) {
                Log.e(TAG, "TIMER DEBUG: Invalid additional seconds: " + additionalSeconds);
                return;
            }

            // Add the additional seconds to remaining time
            int oldRemainingTime = remainingTimeInSeconds;
            remainingTimeInSeconds += additionalSeconds;

            // Reset warning flag since we have more time now
            if (remainingTimeInSeconds > 60) {
                hasShownWarning = false;
                Log.d(TAG, "TIMER DEBUG: Reset warning flag since remaining time > 60 seconds");
            }

            Log.d(TAG, "TIMER DEBUG: Extended timer from " + oldRemainingTime + " to " + remainingTimeInSeconds + " seconds");
            Log.d(TAG, "TIMER DEBUG: Added " + additionalSeconds + " seconds (" + (additionalSeconds / 60) + " minutes)");

            // Save the updated state
            try {
                long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                int elapsedSeconds = (int) (elapsedMillis / 1000);
                pref.saveVpnTimerState(timerStartTime, elapsedSeconds, false, remainingTimeInSeconds);
                Log.d(TAG, "TIMER DEBUG: Saved timer state successfully");
            } catch (Exception saveError) {
                Log.e(TAG, "TIMER DEBUG: Error saving timer state", saveError);
            }

            // Update notification immediately
            try {
                updateNotification("VPN timer: " + formatTime(remainingTimeInSeconds));
                Log.d(TAG, "TIMER DEBUG: Updated notification successfully");
            } catch (Exception notificationError) {
                Log.e(TAG, "TIMER DEBUG: Error updating notification", notificationError);
            }

            // Broadcast the timer update
            try {
                broadcastTimerUpdate(remainingTimeInSeconds, false);
                Log.d(TAG, "TIMER DEBUG: Broadcasted timer update successfully");
            } catch (Exception broadcastError) {
                Log.e(TAG, "TIMER DEBUG: Error broadcasting timer update", broadcastError);
            }

            // Broadcast extension notification for UI
            try {
                Intent extensionIntent = new Intent("vpn_timer_extended");
                extensionIntent.putExtra("additional_seconds", additionalSeconds);
                extensionIntent.putExtra("new_remaining_time", remainingTimeInSeconds);
                LocalBroadcastManager.getInstance(this).sendBroadcast(extensionIntent);
                Log.d(TAG, "TIMER DEBUG: Broadcasted extension notification successfully");
            } catch (Exception extensionBroadcastError) {
                Log.e(TAG, "TIMER DEBUG: Error broadcasting extension notification", extensionBroadcastError);
            }

            Log.d(TAG, "TIMER DEBUG: Timer extension completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "TIMER DEBUG: Critical error extending timer", e);
        }
    }

    private void updateNotification(String contentText) {
        try {
            // CRITICAL FIX: Prevent notification spam with throttling and duplicate checking
            long currentTime = System.currentTimeMillis();

            // Skip update if content hasn't changed
            if (contentText.equals(lastNotificationText)) {
                return;
            }

            // Skip update if too soon since last update (throttling)
            if (currentTime - lastNotificationUpdate < NOTIFICATION_UPDATE_THROTTLE_MS) {
                return;
            }

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager == null) {
                Log.e(TAG, "NotificationManager is null, cannot update notification");
                return;
            }

            Intent notificationIntent = new Intent(this, MainActivity.class);
            PendingIntent pendingIntent;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_MUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
            }

            Notification.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder = new Notification.Builder(this, CHANNEL_ID);
            } else {
                builder = new Notification.Builder(this);
            }

            Notification.Builder notificationBuilder = builder
                    .setContentTitle("VPN Timer Active")
                    .setContentText(contentText)
                    .setSmallIcon(R.drawable.fivegsmartvpn)
                    .setContentIntent(pendingIntent)
                    .setOngoing(true)
                    .setAutoCancel(false)
                    .setPriority(Notification.PRIORITY_LOW)
                    .setCategory(Notification.CATEGORY_SERVICE)
                    .setVisibility(Notification.VISIBILITY_PUBLIC)
                    .setOnlyAlertOnce(true)
                    .setSound(null); // Disable sound for API < 26

            // No .setSilent(true) — that method doesn't exist!

            Notification notification = notificationBuilder.build();

            notificationManager.notify(NOTIFICATION_ID, notification);

            // Update tracking variables
            lastNotificationText = contentText;
            lastNotificationUpdate = currentTime;

            Log.d(TAG, "Silent notification updated: " + contentText);
        } catch (Exception e) {
            Log.e(TAG, "Error updating notification", e);
        }
    }


    private void showOneMinuteWarningNotification() {
        try {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            
            // Create intent for when notification is tapped
            Intent notificationIntent = new Intent(this, VpnWarningActivity.class);
            notificationIntent.putExtra("remaining_time", remainingTimeInSeconds);
            notificationIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            
            PendingIntent pendingIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivity(this, 1, notificationIntent, PendingIntent.FLAG_MUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivity(this, 1, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            
            // Build warning notification
            Notification.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder = new Notification.Builder(this, WARNING_CHANNEL_ID);
            } else {
                builder = new Notification.Builder(this);
            }
            
            builder.setSmallIcon(R.drawable.fivegsmartvpn)
                   .setContentTitle("VPN Time Warning")
                   .setContentText("Only 1 minute remaining! Tap to extend time.")
                   .setAutoCancel(true)
                   .setContentIntent(pendingIntent)
                   .setPriority(Notification.PRIORITY_HIGH)
                   .setDefaults(Notification.DEFAULT_ALL);
            
            notificationManager.notify(9999, builder.build());
            Log.d(TAG, "1-minute warning notification shown from service");
        } catch (Exception e) {
            Log.e(TAG, "Error showing warning notification", e);
        }
    }
    
    private void broadcastTimerUpdate(int timeValue, boolean isPremiumTimer) {
        Intent intent = new Intent("vpn_timer_update");
        intent.putExtra("time_value", timeValue);
        intent.putExtra("is_premium", isPremiumTimer);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }
    
    private void broadcastVpnDisconnect() {
        Intent intent = new Intent("vpn_timer_expired");
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    /**
     * Directly disconnect VPN from the service when timer expires
     */
    private void disconnectVpnDirectly() {
        try {
            Log.d(TAG, "Attempting to disconnect VPN directly from timer service");

            // Method 1: Try to stop OpenVPN service directly
            Intent vpnServiceIntent = new Intent(this, de.blinkt.openvpn.core.OpenVPNService.class);
            vpnServiceIntent.setAction(de.blinkt.openvpn.core.OpenVPNService.DISCONNECT_VPN);
            startService(vpnServiceIntent);

            // Method 2: Update VPN state in preferences
            Pref pref = new Pref(this);
            pref.saveVpnConnectionState(false);

            // Method 3: Send broadcast to disconnect VPN
            Intent disconnectIntent = new Intent("DISCONNECT_VPN");
            sendBroadcast(disconnectIntent);

            Log.d(TAG, "VPN disconnect commands sent from timer service");

        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting VPN directly", e);

            // Fallback: Try to use ProfileManager if available
            try {
                de.blinkt.openvpn.core.ProfileManager.setConntectedVpnProfileDisconnected(this);
                Log.d(TAG, "VPN disconnected using ProfileManager fallback");
            } catch (Exception fallbackError) {
                Log.e(TAG, "Fallback VPN disconnect also failed", fallbackError);
            }
        }
    }
    
    private String formatTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int secs = seconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, secs);
    }
    
    private String formatElapsedTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        return String.format("%02d:%02d elapsed", hours, minutes);
    }

    /**
     * Start periodic health check to ensure service stays alive
     */
    private void startHealthCheck() {
        if (healthCheckRunnable != null) {
            healthCheckHandler.removeCallbacks(healthCheckRunnable);
        }

        healthCheckRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // Check if service should still be running
                    if (shouldContinueRunning()) {
                        Log.d(TAG, "Health check: Service is healthy and should continue");

                        // CRITICAL FIX: Remove notification update from health check to prevent spam
                        // The timer runnable already handles notification updates
                        // Only ensure foreground state is maintained without updating content

                        // Schedule next health check in 30 seconds
                        healthCheckHandler.postDelayed(this, 30000);
                    } else {
                        Log.d(TAG, "Health check: Service should stop");
                        stopSelf();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in health check", e);
                    // Continue health checks even if there's an error
                    healthCheckHandler.postDelayed(this, 30000);
                }
            }
        };

        // Start first health check after 30 seconds
        healthCheckHandler.postDelayed(healthCheckRunnable, 30000);
        Log.d(TAG, "Health check started");
    }

    /**
     * Stop health check
     */
    private void stopHealthCheck() {
        if (healthCheckRunnable != null && healthCheckHandler != null) {
            healthCheckHandler.removeCallbacks(healthCheckRunnable);
            healthCheckRunnable = null;
            Log.d(TAG, "Health check stopped");
        }
    }
}
