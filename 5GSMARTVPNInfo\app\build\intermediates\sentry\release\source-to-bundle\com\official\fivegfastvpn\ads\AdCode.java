package com.official.fivegfastvpn.ads;

import android.app.Activity;
import android.app.ProgressDialog;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.facebook.ads.Ad;
import com.facebook.ads.AdError;
import com.facebook.ads.AdSettings;
import com.facebook.ads.AudienceNetworkAds;
import com.facebook.ads.BuildConfig;
import com.facebook.ads.NativeAdLayout;
import com.facebook.ads.NativeAdListener;
import com.facebook.ads.RewardedVideoAd;

import com.google.android.ads.nativetemplates.TemplateView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.nativead.NativeAdOptions;
import com.google.android.gms.ads.rewarded.RewardedAd;
import com.official.fivegfastvpn.R;

//Developer :--Md Sadrul Hasan Dider
public class AdCode {

    public static String ADMOB = "admob";
    public static String FACEBOOK = "facebook";
    public static InterstitialAd mInterstitialAd;
    public static RewardedAd rewardedAd;
    public static com.facebook.ads.InterstitialAd fInterstitial;
    public static RewardedVideoAd fRewarded;


//    ============== Banner =====================

    public static void setBanner(LinearLayout bannerContainer, Activity context) {
        if (AdsHelper.isDisableAds(context)) {
            bannerContainer.setVisibility(View.GONE);
            return;
        }

        if (AdsHelper.banner_type.contains(ADMOB)) {
            Admob.setBanner(context, bannerContainer);
        } else if (AdsHelper.banner_type.contains(FACEBOOK)) {
            Facebook.setBanner(context, bannerContainer);
        } else {
            bannerContainer.setVisibility(View.GONE);
        }

    }

//    ====================== Inter ====================

    public static void loadInt(Activity activity) {
        if (AdsHelper.isDisableAds(activity)) {
            return;
        }

        if (AdsHelper.interstitial_type.contains(ADMOB)) {
            Admob.loadInt(activity);
        } else if (AdsHelper.interstitial_type.contains(FACEBOOK)) {
            Facebook.loadInt(activity);
        }
    }

    public static void showInt(Activity activity, AdCall adCall) {

        if (AdsHelper.isDisableAds(activity)) {
            adCall.next();
            return;
        }

        ProgressDialog loader = new ProgressDialog(activity);
        loader.setTitle("Showing Ad....");

        if ((AdsHelper.interstitial_type.contains(ADMOB) && AdCode.mInterstitialAd != null) || (AdsHelper.interstitial_type.contains(FACEBOOK) && AdCode.fInterstitial != null)) {

            loader.show();

            try {
                new Handler(Looper.getMainLooper()).postDelayed(() -> activity.runOnUiThread(() -> {
                    loader.dismiss();

                    if (AdsHelper.interstitial_type.contains(ADMOB)) {

                        Admob.showInt(activity, adCall);

                    } else if (AdsHelper.interstitial_type.contains(FACEBOOK)) {

                        Facebook.showInt(activity, adCall);
                    }

                }), 500);
            } catch (Exception e) {
                loader.dismiss();
                if (AdsHelper.interstitial_type.contains(ADMOB)) {
                    Admob.showInt(activity, adCall);
                } else if (AdsHelper.interstitial_type.contains(FACEBOOK)) {
                    Facebook.showInt(activity, adCall);
                }
            }
        } else {
            adCall.next();
        }
    }

//    ================== Rewarded ==================

    public static void loadRew(Activity activity) {
        if (AdsHelper.isDisableAds(activity)) {
            return;
        }

        if (AdsHelper.rewarded_type.contains(ADMOB)) {
            Admob.loadRew(activity);
        } else if (AdsHelper.rewarded_type.contains(FACEBOOK)) {
            Facebook.loadRew(activity);
        }
    }

    public static void showRew(Activity activity, AdCall adCall, boolean skippable) {

        ProgressDialog loader = new ProgressDialog(activity);
        loader.setTitle("Showing Ad....");

        if (skippable) {

            if ((AdsHelper.rewarded_type.contains(ADMOB) && AdCode.rewardedAd != null) || (AdsHelper.rewarded_type.contains(FACEBOOK) && AdCode.fRewarded != null)) {
                loader.show();
                try {
                    new Handler(Looper.getMainLooper()).postDelayed(() -> activity.runOnUiThread(() -> {
                        loader.dismiss();
                        if (AdsHelper.rewarded_type.contains(ADMOB)) {
                            Admob.showRewarded(activity, adCall);
                        } else if (AdsHelper.rewarded_type.contains(FACEBOOK)) {
                            Facebook.showRew(activity, adCall);
                        }
                    }), 500);
                } catch (Exception e) {
                    loader.dismiss();
                    if (AdsHelper.rewarded_type.contains(ADMOB)) {
                        Admob.showRewarded(activity, adCall);
                    } else if (AdsHelper.rewarded_type.contains(FACEBOOK)) {
                        Facebook.showRew(activity, adCall);
                    }
                }

            } else {
                adCall.next();
            }
        } else {

            if ((AdsHelper.rewarded_type.contains(ADMOB) && AdCode.rewardedAd != null) || (AdsHelper.rewarded_type.contains(FACEBOOK) && AdCode.fRewarded != null)) {

                loader.show();

                try {
                    new Handler(Looper.getMainLooper()).postDelayed(() -> activity.runOnUiThread(() -> {

                        loader.dismiss();

                        if (AdsHelper.rewarded_type.contains(ADMOB)) {

                            Admob.showRewarded(activity, adCall);

                        } else if (AdsHelper.rewarded_type.contains(FACEBOOK)) {

                            Facebook.showRew(activity, adCall);
                        }

                    }), 500);

                } catch (Exception e) {
                    loader.dismiss();
                    if (AdsHelper.rewarded_type.contains(ADMOB)) {
                        Admob.showRewarded(activity, adCall);
                    } else if (AdsHelper.rewarded_type.contains(FACEBOOK)) {
                        Facebook.showRew(activity, adCall);
                    }
                }
            } else {
                Toast.makeText(activity, "Try After some time", Toast.LENGTH_SHORT).show();
            }
        }


    }


//    =============================== Native =======================

    public static void loadNativeAd(RelativeLayout native_main_container, FrameLayout adLayout, NativeAdLayout fNative, RelativeLayout placeholder, Activity context) {
        if (AdsHelper.isDisableAds(context)) {
            native_main_container.setVisibility(View.GONE);
            return;
        }

        if (adLayout.getVisibility() == View.VISIBLE || placeholder.getVisibility() == View.GONE || fNative.getVisibility() == View.VISIBLE) {
            return;
        }
        adLayout.setVisibility(View.GONE);
        placeholder.setVisibility(View.VISIBLE);
        fNative.setVisibility(View.GONE);

        if (AdsHelper.native_type.contains(ADMOB)) {
            // Load AdMob Native Ad
            AdLoader.Builder builder = new AdLoader.Builder(context, AdsHelper.admob_native);
            builder.forNativeAd(unifiedNativeAd -> {
                View admobNativeAdView = LayoutInflater.from(context).inflate(R.layout.admob_nativead_templete, null);

                // Find the TemplateView inside the inflated layout
                TemplateView templateView = admobNativeAdView.findViewById(R.id.my_template);

                // Set the AdMob native ad to the TemplateView
                templateView.setNativeAd(unifiedNativeAd);

                // Make the TemplateView visible
                templateView.setVisibility(View.VISIBLE);

                adLayout.removeAllViews();
                adLayout.addView(admobNativeAdView);
            }).build();

            NativeAdOptions adOptions = new NativeAdOptions.Builder().build();
            builder.withNativeAdOptions(adOptions);
            builder.withAdListener(new AdListener() {
                public void onAdLoaded() {
                    super.onAdLoaded();
                    adLayout.setVisibility(View.VISIBLE);
                    placeholder.setVisibility(View.GONE);
                }



            });
            AdLoader adLoader = builder.build();

            adLoader.loadAd(new AdRequest.Builder().build());

        } else if (AdsHelper.native_type.contains(FACEBOOK)) {
            //Facebook

            fNative.setVisibility(View.VISIBLE);
            AudienceNetworkAds.initialize(context);
            if (BuildConfig.DEBUG) {
                AdSettings.setTestMode(true);
            }

            com.facebook.ads.NativeAd nativeAd = new com.facebook.ads.NativeAd(context, AdsHelper.facebook_native);
            NativeAdListener nativeAdListener = new NativeAdListener() {
                @Override
                public void onMediaDownloaded(Ad ad) {
                }

                @Override
                public void onError(Ad ad, AdError adError) {

                }

                @Override
                public void onAdLoaded(Ad ad) {
                    if (nativeAd != ad) {
                        return;
                    }
                    Facebook.inflateAd(nativeAd, fNative, context);
                }

                @Override
                public void onAdClicked(Ad ad) {
                }

                @Override
                public void onLoggingImpression(Ad ad) {
                }
            };
            nativeAd.loadAd(nativeAd.buildLoadAdConfig().withAdListener(nativeAdListener).build());
        } else {
            native_main_container.setVisibility(View.GONE);
        }

    }


}
