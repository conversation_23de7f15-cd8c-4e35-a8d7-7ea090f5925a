package com.official.fivegfastvpn.adapter;

import android.graphics.Color;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.model.NotificationModel;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for displaying notifications in RecyclerView
 */
public class NotificationAdapter extends RecyclerView.Adapter<NotificationAdapter.NotificationViewHolder> {

    private List<NotificationModel> notifications;
    private OnNotificationClickListener listener;

    public interface OnNotificationClickListener {
        void onNotificationClick(NotificationModel notification);
    }

    public NotificationAdapter(List<NotificationModel> notifications, OnNotificationClickListener listener) {
        this.notifications = notifications != null ? notifications : new ArrayList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public NotificationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_notification, parent, false);
        return new NotificationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull NotificationViewHolder holder, int position) {
        if (notifications != null && position < notifications.size()) {
            NotificationModel notification = notifications.get(position);
            android.util.Log.d("NotificationAdapter", "Binding notification at position " + position +
                ": ID=" + notification.getId() +
                ", Title='" + notification.getTitle() + "'" +
                ", Message='" + notification.getMessage() + "'");
            holder.bind(notification, listener);
        } else {
            android.util.Log.w("NotificationAdapter", "Cannot bind at position " + position + ", notifications size: " + (notifications != null ? notifications.size() : "null"));
        }
    }

    @Override
    public int getItemCount() {
        int count = notifications != null ? notifications.size() : 0;
        android.util.Log.d("NotificationAdapter", "getItemCount() returning: " + count);
        return count;
    }

    public static class NotificationViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private TextView titleTextView;
        private TextView messageTextView;
        private TextView timeTextView;
        private TextView categoryTextView;
        private TextView priorityTextView;
        private View unreadIndicator;

        public NotificationViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.cardView);
            titleTextView = itemView.findViewById(R.id.textTitle);
            messageTextView = itemView.findViewById(R.id.textMessage);
            timeTextView = itemView.findViewById(R.id.textTime);
            categoryTextView = itemView.findViewById(R.id.textCategory);
            priorityTextView = itemView.findViewById(R.id.textPriority);
            unreadIndicator = itemView.findViewById(R.id.unreadIndicator);
        }

        public void bind(NotificationModel notification, OnNotificationClickListener listener) {
            titleTextView.setText(notification.getTitle());

            // Use preview if available, otherwise use full message
            String displayMessage = notification.getMessagePreview();
            if (displayMessage == null || displayMessage.isEmpty()) {
                displayMessage = notification.getMessage();
                if (displayMessage.length() > 100) {
                    displayMessage = displayMessage.substring(0, 100) + "...";
                }
            }
            messageTextView.setText(displayMessage);

            // Format and display time
            timeTextView.setText(formatTime(notification.getCreatedAt()));

            // Set category
            String category = notification.getCategory();
            if (category != null && !category.isEmpty()) {
                categoryTextView.setText(category.toUpperCase());
                categoryTextView.setVisibility(View.VISIBLE);
                categoryTextView.setTextColor(Color.parseColor(notification.getCategoryColor()));
            } else {
                categoryTextView.setVisibility(View.GONE);
            }

            // Set priority
            String priority = notification.getPriority();
            if (priority != null && !priority.isEmpty() && !"normal".equals(priority)) {
                priorityTextView.setText(priority.toUpperCase());
                priorityTextView.setVisibility(View.VISIBLE);
                priorityTextView.setTextColor(Color.parseColor(notification.getPriorityColor()));

                // Set background for high priority notifications
                if ("urgent".equals(priority) || "high".equals(priority)) {
                    priorityTextView.setBackgroundResource(R.drawable.priority_badge_bg);
                }
            } else {
                priorityTextView.setVisibility(View.GONE);
            }

            // Set read/unread state
            if (notification.isRead()) {
                unreadIndicator.setVisibility(View.GONE);
                titleTextView.setTypeface(null, Typeface.NORMAL);
                messageTextView.setTypeface(null, Typeface.NORMAL);
                cardView.setCardElevation(2f);
                cardView.setAlpha(0.8f);
            } else {
                unreadIndicator.setVisibility(View.VISIBLE);
                titleTextView.setTypeface(null, Typeface.BOLD);
                messageTextView.setTypeface(null, Typeface.NORMAL);
                cardView.setCardElevation(4f);
                cardView.setAlpha(1.0f);
            }

            // Set click listener
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onNotificationClick(notification);
                }
            });

            // Add ripple effect for better UX
            cardView.setClickable(true);
            cardView.setFocusable(true);
        }

        private String formatTime(String createdAt) {
            if (createdAt == null || createdAt.isEmpty()) {
                return "";
            }

            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                Date date = inputFormat.parse(createdAt);

                if (date == null) return createdAt;

                long now = System.currentTimeMillis();
                long notificationTime = date.getTime();
                long diff = now - notificationTime;

                // Less than 1 minute
                if (diff < 60 * 1000) {
                    return "Just now";
                }
                // Less than 1 hour
                else if (diff < 60 * 60 * 1000) {
                    int minutes = (int) (diff / (60 * 1000));
                    return minutes + "m ago";
                }
                // Less than 24 hours
                else if (diff < 24 * 60 * 60 * 1000) {
                    int hours = (int) (diff / (60 * 60 * 1000));
                    return hours + "h ago";
                }
                // Less than 7 days
                else if (diff < 7 * 24 * 60 * 60 * 1000) {
                    int days = (int) (diff / (24 * 60 * 60 * 1000));
                    return days + "d ago";
                }
                // More than 7 days, show date
                else {
                    SimpleDateFormat outputFormat = new SimpleDateFormat("MMM dd", Locale.getDefault());
                    return outputFormat.format(date);
                }

            } catch (ParseException e) {
                // If parsing fails, try to extract date part
                if (createdAt.length() >= 10) {
                    return createdAt.substring(0, 10);
                }
                return createdAt;
            }
        }
    }

    /**
     * Update a specific notification in the list
     */
    public void updateNotification(NotificationModel updatedNotification) {
        if (notifications != null) {
            for (int i = 0; i < notifications.size(); i++) {
                if (notifications.get(i).getId() == updatedNotification.getId()) {
                    notifications.set(i, updatedNotification);
                    notifyItemChanged(i);
                    break;
                }
            }
        }
    }

    /**
     * Add a new notification to the beginning of the list
     */
    public void addNotification(NotificationModel notification) {
        if (notifications != null) {
            notifications.add(0, notification);
            notifyItemInserted(0);
        }
    }

    /**
     * Remove a notification from the list
     */
    public void removeNotification(int notificationId) {
        if (notifications != null) {
            for (int i = 0; i < notifications.size(); i++) {
                if (notifications.get(i).getId() == notificationId) {
                    notifications.remove(i);
                    notifyItemRemoved(i);
                    break;
                }
            }
        }
    }

    /**
     * Mark all notifications as read
     */
    public void markAllAsRead() {
        if (notifications != null) {
            for (NotificationModel notification : notifications) {
                notification.setRead(true);
            }
            notifyDataSetChanged();
        }
    }

    /**
     * Get unread count
     */
    public int getUnreadCount() {
        if (notifications == null) return 0;

        int count = 0;
        for (NotificationModel notification : notifications) {
            if (!notification.isRead()) {
                count++;
            }
        }
        return count;
    }

    /**
     * Filter notifications by category
     */
    public void filterByCategory(String category) {
        // This would require maintaining a separate filtered list
        // For now, this is a placeholder for future implementation
    }

    /**
     * Filter notifications by priority
     */
    public void filterByPriority(String priority) {
        // This would require maintaining a separate filtered list
        // For now, this is a placeholder for future implementation
    }
}
