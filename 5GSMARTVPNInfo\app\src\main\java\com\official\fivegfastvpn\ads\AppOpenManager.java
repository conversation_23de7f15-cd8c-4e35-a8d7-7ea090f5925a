package com.official.fivegfastvpn.ads;

import static androidx.core.content.ContentProviderCompat.requireContext;
import static androidx.core.content.ContextCompat.startActivity;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.appopen.AppOpenAd;
import com.official.fivegfastvpn.MainActivity;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.SplashActivity;

public class AppOpenManager implements LifecycleObserver, Application.ActivityLifecycleCallbacks {
    private static final String LOG_TAG = "AppOpenManager";
    private AppOpenAd appOpenAd = null;
    private Activity currentActivity;
    private final Application myApplication;
    private boolean isShowingAd = false;
    private long loadTime = 0;
    private static boolean isShowingAdFromSplash = false;
    private static boolean hasMovedToMainActivity = false;
    private boolean hasShownAdForActivity = false;

    private OnAppOpenAdLoadCallback adLoadCallback;
    private SplashActivity splashActivityInstance; // Store SplashActivity instance

    public interface OnAppOpenAdLoadCallback {
        void onAdLoaded();
        void onAdFailedToLoad(String error);
    }

    public void setAdLoadCallback(OnAppOpenAdLoadCallback callback) {
        this.adLoadCallback = callback;
    }

    public AppOpenManager(Application myApplication) {
        this.myApplication = myApplication;
        this.myApplication.registerActivityLifecycleCallbacks(this);
        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);
        hasMovedToMainActivity = false;
        Log.d(LOG_TAG, "Called AppOpenManager constructor");
    }

    public void initializeAd() {
        Log.d(LOG_TAG, "Initializing App Open Ad");
        Log.d(LOG_TAG, "Open Ad ID: " + AdsHelper.admob_open_ad);
        Log.d(LOG_TAG, "Open Ad Enabled: " + AdsHelper.isOpenAdEnabled);
        Log.d(LOG_TAG, "Ads Enabled: " + AdsHelper.isAds);
        Log.d(LOG_TAG, "AdMob App ID: " + AdsHelper.admob_id);

        // Enhanced validation
        if (AdsHelper.admob_open_ad == null || AdsHelper.admob_open_ad.isEmpty()) {
            Log.w(LOG_TAG, "Open Ad ID is null or empty");
            if (adLoadCallback != null) {
                adLoadCallback.onAdFailedToLoad("Open Ad ID is null or empty");
            }
            return;
        }

        if (!AdsHelper.isOpenAdEnabled) {
            Log.d(LOG_TAG, "Open ads are disabled in configuration");
            if (adLoadCallback != null) {
                adLoadCallback.onAdFailedToLoad("Open ads disabled in configuration");
            }
            return;
        }

        if (!AdsHelper.isAds) {
            Log.d(LOG_TAG, "Ads are globally disabled");
            if (adLoadCallback != null) {
                adLoadCallback.onAdFailedToLoad("Ads globally disabled");
            }
            return;
        }

        Log.d(LOG_TAG, "All validations passed, proceeding to fetch ad");
        fetchAd();
    }

    public void fetchAd() {
        if (isAdAvailable()) {
            Log.d(LOG_TAG, "Ad already available, skipping fetch");
            if (adLoadCallback != null) {
                adLoadCallback.onAdLoaded();
            }
            return;
        }

        Log.d(LOG_TAG, "Fetching new ad");
        Log.d(LOG_TAG, "Open Ad ID: " + AdsHelper.admob_open_ad);

        try {
            AdRequest request = new AdRequest.Builder().build();
            AppOpenAd.load(
                    myApplication,
                    AdsHelper.admob_open_ad,
                    request,
                    new AppOpenAd.AppOpenAdLoadCallback() {
                        @Override
                        public void onAdLoaded(@NonNull AppOpenAd ad) {
                            appOpenAd = ad;
                            loadTime = System.currentTimeMillis();
                            Log.d(LOG_TAG, "App Open Ad loaded successfully");

                            if (adLoadCallback != null) {
                                adLoadCallback.onAdLoaded();
                            }
                        }

                        @Override
                        public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                            Log.e(LOG_TAG, "App Open Ad failed to load");
                            Log.e(LOG_TAG, "Error Code: " + loadAdError.getCode());
                            Log.e(LOG_TAG, "Error Message: " + loadAdError.getMessage());
                            Log.e(LOG_TAG, "Error Domain: " + loadAdError.getDomain());
                            Log.e(LOG_TAG, "Error Cause: " + loadAdError.getCause());
                            Log.e(LOG_TAG, "Ad Unit ID used: " + AdsHelper.admob_open_ad);

                            String errorDetails = "Error Code " + loadAdError.getCode() + ": ";
                            switch (loadAdError.getCode()) {
                                case 0:
                                    errorDetails += "Internal Error - Something went wrong internally";
                                    break;
                                case 1:
                                    errorDetails += "Invalid Request - Check ad unit ID and app configuration";
                                    break;
                                case 2:
                                    errorDetails += "Network Error - Check internet connection";
                                    break;
                                case 3:
                                    errorDetails += "No Fill - No ads available to show (common in test mode)";
                                    break;
                                default:
                                    errorDetails += loadAdError.getMessage();
                                    break;
                            }
                            Log.w(LOG_TAG, errorDetails);

                            appOpenAd = null;
                            if (adLoadCallback != null) {
                                adLoadCallback.onAdFailedToLoad(errorDetails);
                            }
                        }
                    });
        } catch (Exception e) {
            Log.e(LOG_TAG, "Error loading App Open Ad: " + e.getMessage());
            appOpenAd = null;
            if (adLoadCallback != null) {
                adLoadCallback.onAdFailedToLoad(e.getMessage());
            }
        }
    }

    private boolean wasLoadTimeLessThanNHoursAgo(long numHours) {
        long dateDifference = System.currentTimeMillis() - loadTime;
        long numMilliSecondsPerHour = 3600000;
        boolean result = (dateDifference < (numMilliSecondsPerHour * numHours));
        Log.d(LOG_TAG, "wasLoadTimeLessThanNHoursAgo: " + result + " (diff: " + dateDifference + "ms)");
        return result;
    }

    private boolean isAdAvailable() {
        boolean result = appOpenAd != null && wasLoadTimeLessThanNHoursAgo(4);
        Log.d(LOG_TAG, "isAdAvailable: " + result);
        return result;
    }

    public void showAdFromSplash(Activity activity) {
        // CRITICAL FIX: Reset state when splash is called again (app reopened)
        if (activity instanceof SplashActivity) {
            Log.d(LOG_TAG, "Resetting AppOpenManager state for new splash session");
            hasMovedToMainActivity = false;
            hasShownAdForActivity = false;
            isShowingAdFromSplash = false;
        }

        if (hasMovedToMainActivity) {
            Log.d(LOG_TAG, "Already moved to MainActivity, skipping ad");
            return;
        }
        currentActivity = activity;
        isShowingAdFromSplash = true;
        hasShownAdForActivity = false;
        if (activity instanceof SplashActivity) {
            splashActivityInstance = (SplashActivity) activity; // Store SplashActivity
        } else {
            splashActivityInstance = null; // Ensure it's null if not SplashActivity
        }
        showAdIfAvailable();
    }

    public void showAdIfAvailable() {
        if (hasMovedToMainActivity) {
            Log.d(LOG_TAG, "Already moved to MainActivity, skipping ad show");
            return;
        }

        Log.d(LOG_TAG, "showAdIfAvailable called");
        Log.d(LOG_TAG, "isShowingAd: " + isShowingAd);
        Log.d(LOG_TAG, "isAdAvailable: " + isAdAvailable());
        Log.d(LOG_TAG, "hasShownAdForActivity: " + hasShownAdForActivity);
        Log.d(LOG_TAG, "appOpenAd: " + (appOpenAd != null));
        Log.d(LOG_TAG, "currentActivity: " + (currentActivity != null));
        Log.d(LOG_TAG, "isAds: " + AdsHelper.isAds);
        Log.d(LOG_TAG, "isShowingAdFromSplash: " + isShowingAdFromSplash);

        // Only show ad if open ads are specifically enabled and other conditions are met
        if (AdsHelper.isOpenAdEnabled && AdsHelper.isAds && !isShowingAd && isAdAvailable() && !hasMovedToMainActivity) {
            Log.d(LOG_TAG, "Will show ad");
            try {
                FullScreenContentCallback fullScreenContentCallback =
                        new FullScreenContentCallback() {
                            @Override
                            public void onAdDismissedFullScreenContent() {
                                Log.d(LOG_TAG, "Ad was dismissed");
                                appOpenAd = null;
                                isShowingAd = false;
                                hasShownAdForActivity = true;
                                isShowingAdFromSplash = false;

                                // Proceed to main activity and finish splash with improved transition
                                if (splashActivityInstance != null && !hasMovedToMainActivity) {
                                    try {
                                        Log.d(LOG_TAG, "Proceeding to MainActivity after ad dismiss");
                                        hasMovedToMainActivity = true;

                                        // Add a small delay to ensure smooth transition
                                        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                                            // Check if activity is still valid before proceeding
                                            if (!splashActivityInstance.isFinishing() && !splashActivityInstance.isDestroyed()) {
                                                Intent intent = new Intent(splashActivityInstance, MainActivity.class);
                                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                                intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                                                splashActivityInstance.startActivity(intent);
                                                splashActivityInstance.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

                                                // Finish splash activity after a brief delay to ensure smooth transition
                                                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                                                    if (!splashActivityInstance.isFinishing()) {
                                                        splashActivityInstance.finish();
                                                    }
                                                }, 100);
                                            } else {
                                                Log.w(LOG_TAG, "SplashActivity is finishing or destroyed, cannot proceed");
                                            }
                                        }, 200); // Small delay for smooth transition
                                    } catch (Exception e) {
                                        Log.e(LOG_TAG, "Error proceeding to MainActivity after ad dismiss: " + e.getMessage());
                                    }
                                } else {
                                    Log.d(LOG_TAG, "Condition not met to proceed to MainActivity after ad dismiss. splashActivityInstance: " + (splashActivityInstance != null) + ", hasMovedToMainActivity: " + hasMovedToMainActivity);
                                }
                            }

                            @Override
                            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                                Log.d(LOG_TAG, "Failed to show ad: " + adError.getMessage());
                                appOpenAd = null;
                                isShowingAd = false;
                                hasShownAdForActivity = true;
                                isShowingAdFromSplash = false;

                                // Proceed to main activity and finish splash even if ad fails to show
                                if (splashActivityInstance != null && !hasMovedToMainActivity) {
                                    try {
                                        Log.d(LOG_TAG, "Proceeding to MainActivity after ad failed to show");
                                        hasMovedToMainActivity = true;

                                        // Add a small delay to ensure smooth transition
                                        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                                            // Check if activity is still valid before proceeding
                                            if (!splashActivityInstance.isFinishing() && !splashActivityInstance.isDestroyed()) {
                                                Intent intent = new Intent(splashActivityInstance, MainActivity.class);
                                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                                intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                                                splashActivityInstance.startActivity(intent);
                                                splashActivityInstance.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

                                                // Finish splash activity after a brief delay
                                                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                                                    if (!splashActivityInstance.isFinishing()) {
                                                        splashActivityInstance.finish();
                                                    }
                                                }, 100);
                                            } else {
                                                Log.w(LOG_TAG, "SplashActivity is finishing or destroyed, cannot proceed");
                                            }
                                        }, 200);
                                    } catch (Exception e) {
                                        Log.e(LOG_TAG, "Error proceeding to MainActivity after ad failed to show: " + e.getMessage());
                                    }
                                }
                            }

                            @Override
                            public void onAdShowedFullScreenContent() {
                                Log.d(LOG_TAG, "Ad showed fullscreen content");
                                isShowingAd = true;
                            }
                        };

                appOpenAd.setFullScreenContentCallback(fullScreenContentCallback);
                appOpenAd.show(currentActivity);

            } catch (Exception e) {
                Log.e(LOG_TAG, "Error showing App Open Ad: " + e.getMessage());
                appOpenAd = null;
                isShowingAd = false;
                hasShownAdForActivity = true;
                isShowingAdFromSplash = false;

                // Proceed to main activity and finish splash in case of exception
                if (splashActivityInstance != null && !hasMovedToMainActivity) {
                    try {
                        Log.d(LOG_TAG, "Proceeding to MainActivity after error showing ad");
                        hasMovedToMainActivity = true;

                        // Add a small delay to ensure smooth transition
                        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                            // Check if activity is still valid before proceeding
                            if (!splashActivityInstance.isFinishing() && !splashActivityInstance.isDestroyed()) {
                                Intent intent = new Intent(splashActivityInstance, MainActivity.class);
                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                                splashActivityInstance.startActivity(intent);
                                splashActivityInstance.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

                                // Finish splash activity after a brief delay
                                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                                    if (!splashActivityInstance.isFinishing()) {
                                        splashActivityInstance.finish();
                                    }
                                }, 100);
                            } else {
                                Log.w(LOG_TAG, "SplashActivity is finishing or destroyed, cannot proceed");
                            }
                        }, 200);
                    } catch (Exception ex) {
                        Log.e(LOG_TAG, "Error proceeding to MainActivity after exception: " + ex.getMessage());
                    }
                }
            }
        } else {
            Log.d(LOG_TAG, "Cannot show ad - Open Ad Enabled: " + AdsHelper.isOpenAdEnabled + ", Ads: " + AdsHelper.isAds + ", isShowingAd: " + isShowingAd + ", isAdAvailable: " + isAdAvailable() + ", hasMovedToMainActivity: " + hasMovedToMainActivity);

            // Proceed to main activity and finish splash if ad cannot be shown
            if (isShowingAdFromSplash && splashActivityInstance != null && !hasMovedToMainActivity) {
                try {
                    Log.d(LOG_TAG, "Proceeding to MainActivity as ad cannot be shown");
                    hasMovedToMainActivity = true;

                    // Add a small delay to ensure smooth transition
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        // Check if activity is still valid before proceeding
                        if (!splashActivityInstance.isFinishing() && !splashActivityInstance.isDestroyed()) {
                            Intent intent = new Intent(splashActivityInstance, MainActivity.class);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                            splashActivityInstance.startActivity(intent);
                            splashActivityInstance.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

                            // Finish splash activity after a brief delay
                            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                                if (!splashActivityInstance.isFinishing()) {
                                    splashActivityInstance.finish();
                                }
                            }, 100);
                        } else {
                            Log.w(LOG_TAG, "SplashActivity is finishing or destroyed, cannot proceed");
                        }
                    }, 200);
                } catch (Exception e) {
                    Log.e(LOG_TAG, "Error proceeding to MainActivity when ad cannot be shown: " + e.getMessage());
                }
            }
        }
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        Log.d(LOG_TAG, "onActivityCreated: " + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        Log.d(LOG_TAG, "onActivityStarted: " + activity.getClass().getSimpleName());
        currentActivity = activity;
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        Log.d(LOG_TAG, "onActivityResumed: " + activity.getClass().getSimpleName());
        currentActivity = activity;
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        Log.d(LOG_TAG, "onActivityPaused: " + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {
        Log.d(LOG_TAG, "onActivityStopped: " + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        Log.d(LOG_TAG, "onActivitySaveInstanceState: " + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        Log.d(LOG_TAG, "onActivityDestroyed: " + activity.getClass().getSimpleName());
    }
}