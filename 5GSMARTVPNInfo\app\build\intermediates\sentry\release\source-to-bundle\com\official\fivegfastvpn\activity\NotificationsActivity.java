package com.official.fivegfastvpn.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.adapter.NotificationAdapter;
import com.official.fivegfastvpn.api.NotificationApiService;
import com.official.fivegfastvpn.model.NotificationModel;
import com.official.fivegfastvpn.utils.NotificationStorage;

import java.util.ArrayList;
import java.util.List;

/**
 * Activity to display and manage notifications
 */
public class NotificationsActivity extends AppCompatActivity implements NotificationAdapter.OnNotificationClickListener {
    private static final String TAG = "NotificationsActivity";

    private RecyclerView recyclerView;
    private NotificationAdapter adapter;
    private SwipeRefreshLayout swipeRefreshLayout;
    private ProgressBar progressBar;
    private View emptyView;
    private TextView emptyMessage;
    private Button retryButton;
    private TextView markAllReadButton;

    private NotificationApiService apiService;
    private NotificationStorage storage;
    private List<NotificationModel> notifications;

    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMorePages = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate started");
        setContentView(R.layout.activity_notifications);

        // Initialize notifications list first before setting up RecyclerView
        notifications = new ArrayList<>();

        initViews();
        setupToolbar();
        setupRecyclerView();
        setupSwipeRefresh();

        apiService = NotificationApiService.getInstance();
        storage = NotificationStorage.getInstance(this);

        Log.d(TAG, "About to load notifications");
        loadNotifications(true);
        Log.d(TAG, "onCreate completed");
    }

    private void initViews() {
        recyclerView = findViewById(R.id.recyclerViewNotifications);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        progressBar = findViewById(R.id.progressBar);
        emptyView = findViewById(R.id.emptyView);
        emptyMessage = findViewById(R.id.emptyMessage);
        retryButton = findViewById(R.id.retryButton);
        markAllReadButton = findViewById(R.id.markAllReadButton);

        // Verify all views were found
        Log.d(TAG, "Views initialized - recyclerView: " + (recyclerView != null) +
                   ", swipeRefreshLayout: " + (swipeRefreshLayout != null) +
                   ", emptyView: " + (emptyView != null) +
                   ", markAllReadButton: " + (markAllReadButton != null));

        markAllReadButton.setOnClickListener(v -> markAllAsRead());
        retryButton.setOnClickListener(v -> retryLoadNotifications());
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Notifications");
        }
    }

    private void setupRecyclerView() {
        Log.d(TAG, "Setting up RecyclerView with " + notifications.size() + " notifications");
        adapter = new NotificationAdapter(notifications, this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
        Log.d(TAG, "RecyclerView setup complete");

        // Add scroll listener for pagination
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                if (layoutManager != null && !isLoading && hasMorePages) {
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();

                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5) {
                        loadMoreNotifications();
                    }
                }
            }
        });
    }

    private void setupSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener(() -> {
            currentPage = 1;
            hasMorePages = true;
            loadNotifications(true);
        });

        swipeRefreshLayout.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
        );
    }

    private void loadNotifications(boolean refresh) {
        if (isLoading) return;

        isLoading = true;

        if (refresh) {
            progressBar.setVisibility(View.VISIBLE);
            emptyView.setVisibility(View.GONE);
        }

        // Always load from cache first if available
        if (refresh) {
            List<NotificationModel> cachedNotifications = storage.loadNotifications();
            Log.d(TAG, "Loaded " + cachedNotifications.size() + " cached notifications");

            if (!cachedNotifications.isEmpty()) {
                notifications.clear();
                notifications.addAll(cachedNotifications);
                adapter.notifyDataSetChanged();
                updateUI();
                progressBar.setVisibility(View.GONE);
                swipeRefreshLayout.setRefreshing(false);
                isLoading = false;

                Log.d(TAG, "Showing " + notifications.size() + " notifications from cache, cache expired: " + storage.isCacheExpired());

                // Only try to fetch from API if cache is expired
                if (storage.isCacheExpired()) {
                    Log.d(TAG, "Cache expired, will try to fetch fresh data in background");
                    // Try to fetch fresh data in background, but don't block UI
                    isLoading = false; // Reset loading state
                    currentPage = 1; // Reset to first page for fresh data
                    fetchFromApi(true); // Background refresh to replace cached data
                }
                return;
            }
        }

        // No cache available or not a refresh, fetch from API
        fetchFromApi(refresh);
    }

    private void fetchFromApi(boolean refresh) {
        apiService.getNotifications(currentPage, 20, new NotificationApiService.ApiCallback<NotificationApiService.NotificationResponse>() {
            @Override
            public void onSuccess(NotificationApiService.NotificationResponse response) {
                runOnUiThread(() -> {
                    if (refresh) {
                        notifications.clear();
                        currentPage = 1;
                    }

                    // Add notifications while avoiding duplicates
                    List<NotificationModel> newNotifications = response.getNotifications();
                    Log.d(TAG, "Received " + newNotifications.size() + " notifications from API, refresh=" + refresh + ", currentPage=" + currentPage);

                    if (refresh || currentPage == 1) {
                        // For refresh or first page, replace all notifications
                        notifications.clear();
                        notifications.addAll(newNotifications);
                        Log.d(TAG, "Replaced all notifications, total count: " + notifications.size());
                    } else {
                        // For pagination, only add notifications that don't already exist
                        int addedCount = 0;
                        for (NotificationModel newNotification : newNotifications) {
                            if (!containsNotification(notifications, newNotification.getId())) {
                                notifications.add(newNotification);
                                addedCount++;
                            }
                        }
                        Log.d(TAG, "Added " + addedCount + " new notifications, total count: " + notifications.size());
                    }

                    hasMorePages = response.isHasNext();

                    // Save to cache
                    if (refresh || currentPage == 1) {
                        storage.saveNotifications(notifications);
                    }

                    adapter.notifyDataSetChanged();
                    updateUI();

                    progressBar.setVisibility(View.GONE);
                    swipeRefreshLayout.setRefreshing(false);
                    isLoading = false;
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Log.e(TAG, "Error loading notifications: " + error);

                    // If this is a refresh and we have cached data, show it
                    if (refresh) {
                        List<NotificationModel> cachedNotifications = storage.loadNotifications();
                        if (!cachedNotifications.isEmpty()) {
                            notifications.clear();
                            notifications.addAll(cachedNotifications);
                            adapter.notifyDataSetChanged();

                            // Show different messages based on error type
                            if (error.contains("timeout") || error.contains("Connection timeout")) {
                                Toast.makeText(NotificationsActivity.this, "Connection timeout. Showing cached notifications.", Toast.LENGTH_SHORT).show();
                            } else if (error.contains("Cannot reach server") || error.contains("Cannot connect")) {
                                Toast.makeText(NotificationsActivity.this, "Server unavailable. Showing cached notifications.", Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(NotificationsActivity.this, "Network error. Showing cached notifications.", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            // No cached data available - show error state
                            updateUI(true, error);
                            if (error.contains("timeout") || error.contains("Connection timeout")) {
                                Toast.makeText(NotificationsActivity.this, "Connection timeout. Please check your internet connection.", Toast.LENGTH_LONG).show();
                            } else {
                                Toast.makeText(NotificationsActivity.this, "Failed to load notifications: " + error, Toast.LENGTH_LONG).show();
                            }
                        }
                    } else {
                        // Error loading more notifications
                        if (error.contains("timeout")) {
                            Toast.makeText(NotificationsActivity.this, "Connection timeout. Please try again.", Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(NotificationsActivity.this, "Failed to load more notifications", Toast.LENGTH_SHORT).show();
                        }
                    }

                    updateUI();
                    progressBar.setVisibility(View.GONE);
                    swipeRefreshLayout.setRefreshing(false);
                    isLoading = false;
                });
            }
        });
    }

    private void loadMoreNotifications() {
        if (!hasMorePages || isLoading) return;

        currentPage++;
        loadNotifications(false);
    }

    private void updateUI() {
        updateUI(false, null);
    }

    private void updateUI(boolean showError, String errorMessage) {
        Log.d(TAG, "updateUI called - notifications.size(): " + notifications.size() + ", showError: " + showError);

        if (notifications.isEmpty()) {
            Log.d(TAG, "Showing empty view");
            emptyView.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
            markAllReadButton.setVisibility(View.GONE);

            if (showError && errorMessage != null) {
                // Show error state
                emptyMessage.setText("Failed to load notifications. Please check your internet connection and try again.");
                retryButton.setVisibility(View.VISIBLE);
            } else {
                // Show normal empty state
                emptyMessage.setText("You're all caught up! New notifications will appear here.");
                retryButton.setVisibility(View.GONE);
            }
        } else {
            Log.d(TAG, "Showing recycler view with " + notifications.size() + " notifications");
            emptyView.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);

            // Show mark all read button if there are unread notifications
            int unreadCount = storage.getUnreadCount();
            Log.d(TAG, "Unread count: " + unreadCount);
            markAllReadButton.setVisibility(unreadCount > 0 ? View.VISIBLE : View.GONE);
        }
    }

    private void retryLoadNotifications() {
        currentPage = 1;
        hasMorePages = true;
        loadNotifications(true);
    }

    private void markAllAsRead() {
        storage.markAllAsRead();

        // Update adapter to reflect read status
        for (NotificationModel notification : notifications) {
            notification.setRead(true);
        }
        adapter.notifyDataSetChanged();

        markAllReadButton.setVisibility(View.GONE);
        Toast.makeText(this, "All notifications marked as read", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onNotificationClick(NotificationModel notification) {
        // Mark as read when clicked
        if (!notification.isRead()) {
            storage.markAsRead(notification.getId());
            notification.setRead(true);
            adapter.notifyDataSetChanged();

            // Update mark all read button visibility
            int unreadCount = storage.getUnreadCount();
            markAllReadButton.setVisibility(unreadCount > 0 ? View.VISIBLE : View.GONE);
        }

        // TODO: Handle notification click - open details, perform action, etc.
        showNotificationDetails(notification);
    }

    private void showNotificationDetails(NotificationModel notification) {
        // For now, just show a toast with the full message
        // In a real app, you might open a detail activity or dialog
        Toast.makeText(this, notification.getMessage(), Toast.LENGTH_LONG).show();
    }

    /**
     * Helper method to check if a notification with the given ID already exists in the list
     */
    private boolean containsNotification(List<NotificationModel> notifications, int notificationId) {
        for (NotificationModel notification : notifications) {
            if (notification.getId() == notificationId) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh unread count when returning to activity
        updateUI();
    }
}
