package com.official.fivegfastvpn.utils;

//Developer :--<PERSON><PERSON><PERSON>


import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

public class CheckInternetConnection {

    public boolean netCheck(Context context){
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo nInfo = cm.getActiveNetworkInfo();
        boolean isConnected = nInfo != null && nInfo.isConnectedOrConnecting();
        return isConnected;
    }

}
