package com.official.fivegfastvpn.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.official.fivegfastvpn.model.NotificationModel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Local storage manager for notifications
 * Handles caching and persistence of notifications
 */
public class NotificationStorage {
    private static final String TAG = "NotificationStorage";
    private static final String PREF_NAME = "notification_storage";
    private static final String KEY_NOTIFICATIONS = "cached_notifications";
    private static final String KEY_READ_NOTIFICATIONS = "read_notifications";
    private static final String KEY_LAST_SYNC = "last_sync_time";
    private static final String KEY_UNREAD_COUNT = "unread_count";

    private SharedPreferences preferences;
    private static NotificationStorage instance;

    private NotificationStorage(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public static synchronized NotificationStorage getInstance(Context context) {
        if (instance == null) {
            instance = new NotificationStorage(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * Save notifications to local storage
     */
    public void saveNotifications(List<NotificationModel> notifications) {
        try {
            JSONArray jsonArray = new JSONArray();
            for (NotificationModel notification : notifications) {
                JSONObject jsonObject = notificationToJson(notification);
                jsonArray.put(jsonObject);
            }

            preferences.edit()
                    .putString(KEY_NOTIFICATIONS, jsonArray.toString())
                    .putLong(KEY_LAST_SYNC, System.currentTimeMillis())
                    .apply();

            Log.d(TAG, "Saved " + notifications.size() + " notifications to local storage");
        } catch (JSONException e) {
            Log.e(TAG, "Error saving notifications", e);
        }
    }

    /**
     * Load notifications from local storage
     */
    public List<NotificationModel> loadNotifications() {
        List<NotificationModel> notifications = new ArrayList<>();
        
        try {
            String jsonString = preferences.getString(KEY_NOTIFICATIONS, "[]");
            JSONArray jsonArray = new JSONArray(jsonString);

            Set<String> readNotifications = getReadNotificationIds();

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                NotificationModel notification = jsonToNotification(jsonObject);
                
                // Set read status
                notification.setRead(readNotifications.contains(String.valueOf(notification.getId())));
                
                notifications.add(notification);
            }

            Log.d(TAG, "Loaded " + notifications.size() + " notifications from local storage");
        } catch (JSONException e) {
            Log.e(TAG, "Error loading notifications", e);
        }

        return notifications;
    }

    /**
     * Mark a notification as read
     */
    public void markAsRead(int notificationId) {
        Set<String> readNotifications = getReadNotificationIds();
        readNotifications.add(String.valueOf(notificationId));
        
        preferences.edit()
                .putStringSet(KEY_READ_NOTIFICATIONS, readNotifications)
                .apply();

        updateUnreadCount();
        Log.d(TAG, "Marked notification " + notificationId + " as read");
    }

    /**
     * Mark all notifications as read
     */
    public void markAllAsRead() {
        List<NotificationModel> notifications = loadNotifications();
        Set<String> readNotifications = new HashSet<>();
        
        for (NotificationModel notification : notifications) {
            readNotifications.add(String.valueOf(notification.getId()));
        }

        preferences.edit()
                .putStringSet(KEY_READ_NOTIFICATIONS, readNotifications)
                .putInt(KEY_UNREAD_COUNT, 0)
                .apply();

        Log.d(TAG, "Marked all notifications as read");
    }

    /**
     * Get unread notification count
     */
    public int getUnreadCount() {
        List<NotificationModel> notifications = loadNotifications();
        Set<String> readNotifications = getReadNotificationIds();
        
        int unreadCount = 0;
        for (NotificationModel notification : notifications) {
            if (!readNotifications.contains(String.valueOf(notification.getId()))) {
                unreadCount++;
            }
        }

        // Update stored count
        preferences.edit().putInt(KEY_UNREAD_COUNT, unreadCount).apply();
        
        return unreadCount;
    }

    /**
     * Get cached unread count (faster, but may not be accurate)
     */
    public int getCachedUnreadCount() {
        return preferences.getInt(KEY_UNREAD_COUNT, 0);
    }

    /**
     * Update unread count
     */
    private void updateUnreadCount() {
        int count = getUnreadCount();
        preferences.edit().putInt(KEY_UNREAD_COUNT, count).apply();
    }

    /**
     * Get set of read notification IDs
     */
    private Set<String> getReadNotificationIds() {
        return new HashSet<>(preferences.getStringSet(KEY_READ_NOTIFICATIONS, new HashSet<>()));
    }

    /**
     * Get last sync time
     */
    public long getLastSyncTime() {
        return preferences.getLong(KEY_LAST_SYNC, 0);
    }

    /**
     * Check if cache is expired (older than 5 minutes)
     */
    public boolean isCacheExpired() {
        long lastSync = getLastSyncTime();
        long currentTime = System.currentTimeMillis();
        long fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds
        
        return (currentTime - lastSync) > fiveMinutes;
    }

    /**
     * Clear all cached data
     */
    public void clearCache() {
        preferences.edit().clear().apply();
        Log.d(TAG, "Cleared notification cache");
    }

    /**
     * Add a new notification to the cache
     */
    public void addNotification(NotificationModel notification) {
        List<NotificationModel> notifications = loadNotifications();
        
        // Check if notification already exists
        boolean exists = false;
        for (int i = 0; i < notifications.size(); i++) {
            if (notifications.get(i).getId() == notification.getId()) {
                notifications.set(i, notification); // Update existing
                exists = true;
                break;
            }
        }
        
        if (!exists) {
            notifications.add(0, notification); // Add to beginning
        }
        
        saveNotifications(notifications);
        updateUnreadCount();
    }

    /**
     * Convert NotificationModel to JSON
     */
    private JSONObject notificationToJson(NotificationModel notification) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("id", notification.getId());
        json.put("title", notification.getTitle());
        json.put("message", notification.getMessage());
        json.put("messagePreview", notification.getMessagePreview());
        json.put("status", notification.getStatus());
        json.put("sentTo", notification.getSentTo());
        json.put("notificationType", notification.getNotificationType());
        json.put("scheduleType", notification.getScheduleType());
        json.put("scheduledTime", notification.getScheduledTime());
        json.put("recurringInterval", notification.getRecurringInterval());
        json.put("priority", notification.getPriority());
        json.put("category", notification.getCategory());
        json.put("targetAudience", notification.getTargetAudience());
        json.put("deliveryCount", notification.getDeliveryCount());
        json.put("successCount", notification.getSuccessCount());
        json.put("failureCount", notification.getFailureCount());
        json.put("createdAt", notification.getCreatedAt());
        json.put("updatedAt", notification.getUpdatedAt());
        json.put("data", notification.getData());
        return json;
    }

    /**
     * Convert JSON to NotificationModel
     */
    private NotificationModel jsonToNotification(JSONObject json) throws JSONException {
        NotificationModel notification = new NotificationModel();
        notification.setId(json.getInt("id"));
        notification.setTitle(json.getString("title"));
        notification.setMessage(json.getString("message"));
        notification.setMessagePreview(json.optString("messagePreview", ""));
        notification.setStatus(json.getString("status"));
        notification.setSentTo(json.optString("sentTo", ""));
        notification.setNotificationType(json.optString("notificationType", ""));
        notification.setScheduleType(json.optString("scheduleType", ""));
        notification.setScheduledTime(json.optString("scheduledTime", ""));
        notification.setRecurringInterval(json.optString("recurringInterval", ""));
        notification.setPriority(json.optString("priority", "normal"));
        notification.setCategory(json.optString("category", "general"));
        notification.setTargetAudience(json.optString("targetAudience", ""));
        notification.setDeliveryCount(json.optInt("deliveryCount", 0));
        notification.setSuccessCount(json.optInt("successCount", 0));
        notification.setFailureCount(json.optInt("failureCount", 0));
        notification.setCreatedAt(json.getString("createdAt"));
        notification.setUpdatedAt(json.optString("updatedAt", ""));
        notification.setData(json.optString("data", ""));
        return notification;
    }
}
