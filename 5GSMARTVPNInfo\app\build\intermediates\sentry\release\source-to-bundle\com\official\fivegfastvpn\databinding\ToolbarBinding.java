// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ToolbarBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView toolBack;

  @NonNull
  public final RelativeLayout toolBg;

  @NonNull
  public final TextView toolTitle;

  private ToolbarBinding(@NonNull RelativeLayout rootView, @NonNull ImageView toolBack,
      @NonNull RelativeLayout toolBg, @NonNull TextView toolTitle) {
    this.rootView = rootView;
    this.toolBack = toolBack;
    this.toolBg = toolBg;
    this.toolTitle = toolTitle;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ToolbarBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tool_back;
      ImageView toolBack = ViewBindings.findChildViewById(rootView, id);
      if (toolBack == null) {
        break missingId;
      }

      RelativeLayout toolBg = (RelativeLayout) rootView;

      id = R.id.tool_title;
      TextView toolTitle = ViewBindings.findChildViewById(rootView, id);
      if (toolTitle == null) {
        break missingId;
      }

      return new ToolbarBinding((RelativeLayout) rootView, toolBack, toolBg, toolTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
