// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAboutUsBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView aboutAppLogo;

  @NonNull
  public final TextView aboutAppName;

  @NonNull
  public final TextView aboutAppVersion;

  @NonNull
  public final Button aboutCloseButton;

  private DialogAboutUsBinding(@NonNull CardView rootView, @NonNull ImageView aboutAppLogo,
      @NonNull TextView aboutAppName, @NonNull TextView aboutAppVersion,
      @NonNull Button aboutCloseButton) {
    this.rootView = rootView;
    this.aboutAppLogo = aboutAppLogo;
    this.aboutAppName = aboutAppName;
    this.aboutAppVersion = aboutAppVersion;
    this.aboutCloseButton = aboutCloseButton;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAboutUsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAboutUsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_about_us, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAboutUsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.about_app_logo;
      ImageView aboutAppLogo = ViewBindings.findChildViewById(rootView, id);
      if (aboutAppLogo == null) {
        break missingId;
      }

      id = R.id.about_app_name;
      TextView aboutAppName = ViewBindings.findChildViewById(rootView, id);
      if (aboutAppName == null) {
        break missingId;
      }

      id = R.id.about_app_version;
      TextView aboutAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (aboutAppVersion == null) {
        break missingId;
      }

      id = R.id.about_close_button;
      Button aboutCloseButton = ViewBindings.findChildViewById(rootView, id);
      if (aboutCloseButton == null) {
        break missingId;
      }

      return new DialogAboutUsBinding((CardView) rootView, aboutAppLogo, aboutAppName,
          aboutAppVersion, aboutCloseButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
